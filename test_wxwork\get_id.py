import requests
import json
import logging
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class WeComAPI:
    """企业微信API接口工具类"""
    
    def __init__(self, corpid: str, corpsecret: str):
        """
        初始化企业微信API客户端
        
        Args:
            corpid: 企业ID
            corpsecret: 应用的Secret
        """
        self.corpid = corpid
        self.corpsecret = corpsecret
        self.access_token = None
        self.token_expire_time = 0
        
    def get_access_token(self) -> str:
        """获取企业微信API访问令牌"""
        # 检查令牌是否有效
        if self.access_token and self.token_expire_time > time.time() + 60:
            return self.access_token
            
        # 请求新的访问令牌
        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken"
        params = {
            "corpid": self.corpid,
            "corpsecret": self.corpsecret
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            data = response.json()
            
            if data.get("errcode") != 0:
                raise Exception(f"获取访问令牌失败: {data.get('errmsg')}")
                
            self.access_token = data.get("access_token")
            self.token_expire_time = time.time() + data.get("expires_in", 7200)
            logger.info("成功获取访问令牌")
            return self.access_token
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求访问令牌时发生网络错误: {e}")
            raise
        except Exception as e:
            logger.error(f"获取访问令牌失败: {e}")
            raise
    
    def get_userid_by_mobile(self, mobile: str) -> Optional[str]:
        """
        通过手机号获取用户ID
        
        Args:
            mobile: 手机号
            
        Returns:
            用户ID或None
        """
        access_token = self.get_access_token()
        url = f"https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token={access_token}"
        
        payload = {
            "mobile": mobile
        }
        
        try:
            response = requests.post(url, json=payload)
            response.raise_for_status()
            data = response.json()
            
            if data.get("errcode") != 0:
                error_msg = f"查询用户ID失败: {data.get('errmsg')}"
                logger.error(error_msg)
                return None
                
            return data.get("userid")
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求用户ID时发生网络错误: {e}")
            return None
        except Exception as e:
            logger.error(f"查询用户ID失败: {e}")
            return None

if __name__ == "__main__":
    import sys
    import time
    
    # 从命令行参数获取企业ID和应用Secret
    if len(sys.argv) != 3:
        print("用法: python wecom_mobile_to_userid.py <corpid> <corpsecret>")
        sys.exit(1)
        
    corpid = sys.argv[1]
    corpsecret = sys.argv[2]
    
    # 初始化API客户端
    wecom_api = WeComAPI(corpid, corpsecret)
    
    # 示例：查询手机号对应的用户ID
    mobile_number = input("请输入要查询的手机号: ")
    
    start_time = time.time()
    userid = wecom_api.get_userid_by_mobile(mobile_number)
    end_time = time.time()
    
    if userid:
        print(f"查询成功！手机号 {mobile_number} 对应的用户ID是: {userid}")
    else:
        print(f"未找到手机号 {mobile_number} 对应的用户ID")
        
    print(f"查询耗时: {end_time - start_time:.2f}秒")    