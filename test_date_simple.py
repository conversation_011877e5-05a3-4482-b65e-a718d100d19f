#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的日期字段测试 - 基于自动填充成功的经验
"""

import json
import requests
import logging
import time
from datetime import datetime, timedelta

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s: %(message)s")

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)
CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]

def get_access_token(corpid, corpsecret):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_document(access_token, title):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {"doc_name": title, "doc_type": 10}
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")

def add_sheet(access_token, docid, title, index=0):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    data = {"docid": docid, "title": title, "index": index}
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result["properties"]["sheet_id"]
    else:
        raise Exception(f"添加子表失败: {result}")

def add_fields(access_token, docid, sheet_id, fields):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
    data = {"docid": docid, "sheet_id": sheet_id, "fields": fields}
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加字段失败: {result}")

def add_records(access_token, docid, sheet_id, records):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    logging.info(f"添加记录API返回: {result}")
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加记录失败: {result}")

def test_date_simple():
    logging.info("🚀 开始简化日期字段测试...")
    access_token = get_access_token(CORPID, CORPSECRET)
    doc_title = f"简化日期测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, docurl = create_document(access_token, doc_title)
    logging.info(f"✅ 创建文档成功: {docurl}")
    
    sheet_id = add_sheet(access_token, docid, "日期测试", 0)
    logging.info(f"✅ 创建子表成功: {sheet_id}")

    # 添加字段 - 重点测试自动填充和手动设置的区别
    fields = [
        {"field_title": "测试名称", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "自动填充时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}},
        {"field_title": "手动设置时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}}
    ]
    add_fields(access_token, docid, sheet_id, fields)
    logging.info("✅ 添加字段成功")

    # 基于自动填充成功的经验，尝试不同的手动设置方式
    now = datetime.now()
    now_timestamp = int(now.timestamp())
    
    # 测试记录 - 重点测试能成功的格式
    records = [
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试1-不设置手动时间"}],
                # 只设置测试名称，看自动填充是否工作
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试2-空数组"}],
                "手动设置时间": []  # 空数组
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试3-null值"}],
                "手动设置时间": None  # null值
            }
        }
    ]
    
    # 先测试基础记录
    logging.info("📤 添加基础测试记录...")
    add_result = add_records(access_token, docid, sheet_id, records)
    
    # 现在尝试更新记录来设置日期
    logging.info("🔄 尝试通过更新来设置日期...")
    
    if add_result and add_result.get("records"):
        record_id = add_result["records"][0]["record_id"]
        
        # 尝试不同的更新格式
        update_formats = [
            {
                "name": "更新格式1-毫秒时间戳",
                "values": {"手动设置时间": int(now.timestamp() * 1000)}
            },
            {
                "name": "更新格式2-秒时间戳", 
                "values": {"手动设置时间": now_timestamp}
            }
        ]
        
        for fmt in update_formats:
            try:
                url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/update_records?access_token={access_token}"
                data = {
                    "docid": docid,
                    "sheet_id": sheet_id,
                    "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
                    "records": [{
                        "record_id": record_id,
                        "values": fmt["values"]
                    }]
                }
                resp = requests.post(url, json=data)
                result = resp.json()
                logging.info(f"{fmt['name']} 更新结果: {result}")
                
                if result.get("errcode") == 0:
                    logging.info(f"✅ {fmt['name']} 更新成功")
                    break
                else:
                    logging.info(f"❌ {fmt['name']} 更新失败")
                    
            except Exception as e:
                logging.info(f"❌ {fmt['name']} 更新异常: {e}")
    
    # 最终查看结果
    time.sleep(2)
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={access_token}"
    data = {"docid": docid, "sheet_id": sheet_id}
    resp = requests.post(url, json=data)
    result = resp.json()
    
    logging.info("📊 最终结果分析:")
    if result.get("records"):
        for i, record in enumerate(result["records"]):
            values = record.get("values", {})
            test_name = values.get("测试名称", [{}])[0].get("text", "未知")
            auto_time = values.get("自动填充时间", "无")
            manual_time = values.get("手动设置时间", "无")
            
            logging.info(f"记录{i+1}: {test_name}")
            logging.info(f"  自动填充时间: {auto_time}")
            logging.info(f"  手动设置时间: {manual_time}")
            logging.info("---")
    
    print(f"\n🎯 简化测试完成！")
    print(f"📋 文档链接: {docurl}")
    print(f"💡 关键发现:")
    print(f"   1. 自动填充字段工作正常")
    print(f"   2. 手动设置需要找到正确的数据格式")
    print(f"   3. 可能需要通过更新操作来设置日期")
    
    return docurl

if __name__ == "__main__":
    print("📅 简化日期字段测试")
    print("=" * 30)
    
    try:
        doc_url = test_date_simple()
        print(f"\n✅ 测试完成: {doc_url}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
