import requests
import time
from datetime import datetime
import json

# ========== 配置区 ========== 
# 只需填写企业微信corpid和corpsecret，其余自动创建
CORPID = "wwdb219327d340d664"
CORPSECRET = "YLbcAwAfUPe-wwl411yHaVxvl0Yx0sVmlODumWTCGxM"

try:
    with open('../config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
        CORPID = CORPID or config.get('corpid')
        CORPSECRET = CORPSECRET or config.get('corpsecret')
except Exception as e:
    print(f"读取config.json失败: {e}")

# ========== 工具函数 ==========
def get_access_token():
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={CORPID}&corpsecret={CORPSECRET}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_doc_and_sheet(access_token):
    # 1. 创建智能表格文档
    doc_url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    doc_data = {"doc_name": f"日期字段测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}", "doc_type": 10}
    doc_resp = requests.post(doc_url, json=doc_data).json()
    if doc_resp.get("errcode") != 0:
        raise Exception(f"创建文档失败: {doc_resp}")
    docid = doc_resp["docid"]
    url = doc_resp.get("url")
    print(f"✅ 创建文档成功，docid: {docid}")
    if url:
        print(f"🔗 新建文档URL: {url}")
    else:
        print("⚠️ 未获取到文档URL")
    # 2. 创建子表
    add_sheet_url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    sheet_data = {"docid": docid, "properties": {"title": "日期测试表", "index": 0}}
    sheet_resp = requests.post(add_sheet_url, json=sheet_data).json()
    if sheet_resp.get("errcode") != 0:
        raise Exception(f"创建子表失败: {sheet_resp}")
    sheet_id = sheet_resp["properties"]["sheet_id"]
    print(f"✅ 创建子表成功，sheet_id: {sheet_id}")
    # 3. 添加日期字段
    add_field_url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
    field_data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "fields": [
            {
                "field_title": "测试日期",
                "field_type": "FIELD_TYPE_DATE_TIME",
                "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}
            }
        ]
    }
    field_resp = requests.post(add_field_url, json=field_data).json()
    if field_resp.get("errcode") != 0:
        raise Exception(f"添加日期字段失败: {field_resp}")
    print(f"✅ 添加日期字段成功")
    return docid, sheet_id

def get_sheet_records(access_token, docid, sheet_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "offset": 0,
        "limit": 10
    }
    resp = requests.post(url, json=data)
    try:
        result = resp.json()
    except Exception as e:
        print(f"响应解析失败: {e}, 原始响应: {resp.text}")
        return None
    print("\n===== 子表记录内容 =====")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

# ========== 主测试逻辑 ==========
def test_datetime_write():
    access_token = get_access_token()
    docid, sheet_id = create_doc_and_sheet(access_token)
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
    now = datetime.now()
    ts_millis = int(now.timestamp() * 1000)
    ts_millis_str = str(ts_millis)
    print(f"当前时间: {now}, 毫秒: {ts_millis}, 字符串: {ts_millis_str}")
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": [
            {
                "values": {
                    "测试日期": [ts_millis_str]
                }
            }
        ]
    }
    print(f"\n==== 以字符串形式的毫秒级时间戳写入 ====")
    print(f"请求体: {json.dumps(data, ensure_ascii=False)}")
    resp = requests.post(url, json=data)
    try:
        result = resp.json()
    except Exception as e:
        print(f"响应解析失败: {e}, 原始响应: {resp.text}")
        return
    print(f"返回: {result}")
    if result.get('errcode') == 0:
        print("✅ 写入成功，请在企业微信文档中查看实际显示效果！")
    else:
        print("❌ 写入失败！")
    # 新增：获取子表记录内容
    get_sheet_records(access_token, docid, sheet_id)

if __name__ == "__main__":
    test_datetime_write() 