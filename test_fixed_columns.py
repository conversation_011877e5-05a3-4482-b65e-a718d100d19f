#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试企业微信智能表格的固定列功能
"""

import json
import requests
import logging
import time
from datetime import datetime, timedelta
from wxwork_smartsheet_api_client import WxworkSmartsheetApiClient

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s: %(message)s")

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]
USERS = config.get("users", {})

def get_access_token(corpid, corpsecret):
    """获取企业微信访问令牌"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_document(access_token, title):
    """创建测试文档"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {"doc_name": title, "doc_type": 10}
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")

def add_sheet(access_token, docid, title, index=0):
    """添加子表"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    data = {"docid": docid, "title": title, "index": index}
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result["properties"]["sheet_id"]
    else:
        raise Exception(f"添加子表失败: {result}")

def test_fixed_columns_api(access_token, docid, sheet_id, fixed_column_count):
    """测试设置固定列的API"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/set_sheet_properties?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "properties": {
            "frozen_column_count": fixed_column_count  # 尝试设置固定列数
        }
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    logging.info(f"设置固定列API返回: {result}")
    return result

def test_sheet_view_properties(access_token, docid, sheet_id):
    """测试设置表格视图属性"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/update_sheet_view?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "view_properties": {
            "frozen_columns": 2,  # 尝试固定前2列
            "frozen_rows": 1      # 尝试固定第1行
        }
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    logging.info(f"设置视图属性API返回: {result}")
    return result

def create_wide_table_for_testing(access_token, docid, sheet_id):
    """创建一个宽表格用于测试固定列功能"""
    client = WxworkSmartsheetApiClient(access_token)
    
    # 创建多列字段，模拟需要固定列的场景
    fields = [
        # 前两列 - 希望固定的列
        {"field_title": "任务ID", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "任务名称", "field_type": "FIELD_TYPE_TEXT"},
        
        # 后续列 - 可以滚动的列
        {"field_title": "负责人", "field_type": "FIELD_TYPE_USER", "property_user": {"is_multiple": False}},
        {"field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [
             {"text": "未开始", "style": 18},
             {"text": "进行中", "style": 12},
             {"text": "已完成", "style": 16}
         ]}},
        {"field_title": "优先级", "field_type": "FIELD_TYPE_SINGLE_SELECT",
         "property_single_select": {"options": [
             {"text": "高", "style": 1},
             {"text": "中", "style": 12},
             {"text": "低", "style": 18}
         ]}},
        {"field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", "property_progress": {"decimal_places": 0}},
        {"field_title": "创建时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}},
        {"field_title": "截止时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}},
        {"field_title": "备注1", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "备注2", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "备注3", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "备注4", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "扩展字段1", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "扩展字段2", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "扩展字段3", "field_type": "FIELD_TYPE_TEXT"}
    ]
    
    client.add_fields(docid, sheet_id, fields)
    logging.info(f"✅ 添加 {len(fields)} 个字段成功")
    
    # 添加测试数据
    dev_users = USERS.get("开发人员", [])
    dev_user_obj = [{"type": "user", "user_id": dev_users[0]["user_id"]}] if dev_users else []
    
    now = datetime.now()
    records = []
    
    for i in range(1, 11):  # 创建10条测试记录
        deadline_timestamp = str(int((now + timedelta(minutes=i*10)).timestamp() * 1000))
        records.append({
            "values": {
                "任务ID": [{"type": "text", "text": f"TASK-{i:03d}"}],
                "任务名称": [{"type": "text", "text": f"测试任务{i} - 这是一个比较长的任务名称用于测试显示效果"}],
                "负责人": dev_user_obj,
                "任务状态": [{"type": "single_select", "text": ["未开始", "进行中", "已完成"][i % 3]}],
                "优先级": [{"type": "single_select", "text": ["高", "中", "低"][i % 3]}],
                "进度": i * 10,
                "截止时间": deadline_timestamp,
                "备注1": [{"type": "text", "text": f"备注1内容 - 任务{i}的详细说明"}],
                "备注2": [{"type": "text", "text": f"备注2内容 - 更多详细信息"}],
                "备注3": [{"type": "text", "text": f"备注3内容 - 补充说明"}],
                "备注4": [{"type": "text", "text": f"备注4内容 - 其他信息"}],
                "扩展字段1": [{"type": "text", "text": f"扩展1-{i}"}],
                "扩展字段2": [{"type": "text", "text": f"扩展2-{i}"}],
                "扩展字段3": [{"type": "text", "text": f"扩展3-{i}"}]
            }
        })
    
    result = client.add_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", records)
    logging.info(f"✅ 添加 {len(records)} 条测试记录成功")
    return result

def test_field_properties_for_fixed(access_token, docid, sheet_id):
    """测试通过字段属性设置固定列"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/update_field?access_token={access_token}"
    
    # 尝试设置前两个字段为固定
    test_data = [
        {
            "field_title": "任务ID",
            "properties": {
                "is_fixed": True,  # 尝试设置为固定列
                "fixed_position": "left"  # 尝试设置固定位置
            }
        },
        {
            "field_title": "任务名称", 
            "properties": {
                "is_fixed": True,
                "fixed_position": "left"
            }
        }
    ]
    
    for field_data in test_data:
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "field_title": field_data["field_title"],
            "properties": field_data["properties"]
        }
        resp = requests.post(url, json=data)
        result = resp.json()
        logging.info(f"设置字段 {field_data['field_title']} 固定属性: {result}")

def test_alternative_fixed_methods(access_token, docid, sheet_id):
    """测试其他可能的固定列方法"""
    
    # 方法1: 尝试通过表格配置设置
    methods = [
        {
            "name": "方法1-表格配置",
            "url": f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/set_sheet_config?access_token={access_token}",
            "data": {
                "docid": docid,
                "sheet_id": sheet_id,
                "config": {
                    "frozen_column_count": 2,
                    "frozen_row_count": 1
                }
            }
        },
        {
            "name": "方法2-视图设置",
            "url": f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/set_view_config?access_token={access_token}",
            "data": {
                "docid": docid,
                "sheet_id": sheet_id,
                "view_config": {
                    "freeze_columns": 2,
                    "freeze_rows": 1
                }
            }
        },
        {
            "name": "方法3-布局设置",
            "url": f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/set_layout?access_token={access_token}",
            "data": {
                "docid": docid,
                "sheet_id": sheet_id,
                "layout": {
                    "fixed_columns": ["任务ID", "任务名称"],
                    "column_freeze_count": 2
                }
            }
        }
    ]
    
    for method in methods:
        try:
            resp = requests.post(method["url"], json=method["data"])
            result = resp.json()
            logging.info(f"{method['name']} 结果: {result}")
        except Exception as e:
            logging.info(f"{method['name']} 异常: {e}")

def test_fixed_columns():
    """测试固定列功能"""
    logging.info("🚀 开始测试企业微信智能表格固定列功能...")
    
    # 获取访问令牌
    access_token = get_access_token(CORPID, CORPSECRET)
    logging.info("✅ 获取访问令牌成功")
    
    # 创建测试文档
    doc_title = f"固定列功能测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, doc_url = create_document(access_token, doc_title)
    logging.info(f"✅ 创建测试文档成功: {doc_url}")
    
    # 创建子表
    sheet_id = add_sheet(access_token, docid, "固定列测试表", 0)
    logging.info(f"✅ 创建子表成功: {sheet_id}")
    
    # 创建宽表格
    create_wide_table_for_testing(access_token, docid, sheet_id)
    
    # 测试各种固定列的方法
    logging.info("🔍 测试方法1: 设置表格属性")
    test_fixed_columns_api(access_token, docid, sheet_id, 2)
    
    time.sleep(1)
    
    logging.info("🔍 测试方法2: 设置视图属性")
    test_sheet_view_properties(access_token, docid, sheet_id)
    
    time.sleep(1)
    
    logging.info("🔍 测试方法3: 设置字段属性")
    test_field_properties_for_fixed(access_token, docid, sheet_id)
    
    time.sleep(1)
    
    logging.info("🔍 测试方法4: 其他可能的方法")
    test_alternative_fixed_methods(access_token, docid, sheet_id)
    
    print(f"\n🎯 固定列功能测试完成！")
    print(f"📋 测试文档: {doc_url}")
    print(f"💡 测试说明:")
    print(f"   1. 文档包含15个列，前2列（任务ID、任务名称）希望固定")
    print(f"   2. 测试了多种可能的固定列API方法")
    print(f"   3. 请在企业微信中打开文档，左右滑动查看效果")
    print(f"   4. 观察前2列是否在滑动时保持固定")
    print(f"")
    print(f"🔍 验证步骤:")
    print(f"   1. 在手机企业微信中打开文档")
    print(f"   2. 左右滑动查看所有列")
    print(f"   3. 观察任务ID和任务名称列是否始终可见")
    print(f"   4. 如果固定成功，这两列应该不会随滑动消失")
    
    return docid, doc_url

if __name__ == "__main__":
    print("📌 企业微信智能表格固定列功能测试")
    print("=" * 50)
    
    try:
        docid, doc_url = test_fixed_columns()
        print(f"\n✅ 测试完成")
        print(f"📋 文档链接: {doc_url}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
