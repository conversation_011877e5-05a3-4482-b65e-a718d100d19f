import requests
import json
from datetime import datetime

CORPID = "wwdb219327d340d664"
CORPSECRET = "YLbcAwAfUPe-wwl411yHaVxvl0Yx0sVmlODumWTCGxM"

def get_access_token():
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={CORPID}&corpsecret={CORPSECRET}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_doc_and_sheet_with_autofill(access_token):
    # 1. 创建智能表格文档
    doc_url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    doc_data = {"doc_name": f"日期字段自动填充测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}", "doc_type": 10}
    doc_resp = requests.post(doc_url, json=doc_data).json()
    if doc_resp.get("errcode") != 0:
        raise Exception(f"创建文档失败: {doc_resp}")
    docid = doc_resp["docid"]
    url = doc_resp.get("url")
    print(f"✅ 创建文档成功，docid: {docid}")
    if url:
        print(f"🔗 新建文档URL: {url}")
    # 2. 创建子表
    add_sheet_url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    sheet_data = {"docid": docid, "properties": {"title": "日期自动填充表", "index": 0}}
    sheet_resp = requests.post(add_sheet_url, json=sheet_data).json()
    if sheet_resp.get("errcode") != 0:
        raise Exception(f"创建子表失败: {sheet_resp}")
    sheet_id = sheet_resp["properties"]["sheet_id"]
    print(f"✅ 创建子表成功，sheet_id: {sheet_id}")
    # 3. 添加日期字段并开启自动填充
    add_field_url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
    field_data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "fields": [
            {
                "field_title": "自动填充日期",
                "field_type": "FIELD_TYPE_DATE_TIME",
                "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}
            }
        ]
    }
    field_resp = requests.post(add_field_url, json=field_data).json()
    if field_resp.get("errcode") != 0:
        raise Exception(f"添加日期字段失败: {field_resp}")
    print(f"✅ 添加自动填充日期字段成功")
    return docid, sheet_id

def get_sheet_fields(access_token, docid, sheet_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_sheet?access_token={access_token}"
    data = {"docid": docid, "sheet_id": sheet_id}
    resp = requests.post(url, json=data)
    try:
        result = resp.json()
    except Exception as e:
        print(f"响应解析失败: {e}, 原始响应: {resp.text}")
        return
    print("\n===== 子表字段定义 =====")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

def add_empty_record(access_token, docid, sheet_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": [
            {"values": {}}  # 不指定任何字段，触发auto_fill
        ]
    }
    resp = requests.post(url, json=data)
    try:
        result = resp.json()
    except Exception as e:
        print(f"响应解析失败: {e}, 原始响应: {resp.text}")
        return None
    print("\n===== 添加空记录返回 =====")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

def get_sheet_records(access_token, docid, sheet_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "offset": 0,
        "limit": 10
    }
    resp = requests.post(url, json=data)
    try:
        result = resp.json()
    except Exception as e:
        print(f"响应解析失败: {e}, 原始响应: {resp.text}")
        return None
    print("\n===== 子表记录内容 =====")
    print(json.dumps(result, ensure_ascii=False, indent=2))
    return result

def main():
    access_token = get_access_token()
    docid, sheet_id = create_doc_and_sheet_with_autofill(access_token)
    add_empty_record(access_token, docid, sheet_id)
    get_sheet_fields(access_token, docid, sheet_id)
    get_sheet_records(access_token, docid, sheet_id)

if __name__ == "__main__":
    main() 