#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的CSV处理并上传到企业微信
直接调用现有的CSV处理类和企业微信API
"""

import requests
import time
import logging
from datetime import datetime, timedelta
from csv_ocr_validation_handler import CSVDataProcessor
from wxwork_smartsheet_api_client import WxworkSmartsheetApiClient
from typing import List, Dict
import json
import os

# 读取配置
with open(os.path.join(os.path.dirname(__file__), 'config.json'), 'r', encoding='utf-8') as f:
    config = json.load(f)

CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]
CSV_FILES = [config["csv_upload"]["initial_csv"]]
DEADLINE_MINUTES = config.get("deadline_minutes", 10)  # 截止时间间隔（分钟）
REASON_TASK_PERSON_MAP = config["reason_task_person_map"]
USERS = config.get("users", {})

def get_access_token(corpid, corpsecret):
    """获取企业微信访问令牌"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取访问令牌失败: {result}")


def create_document(access_token, title):
    """创建企业微信文档，返回docid和url"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_name": title,
        "doc_type": 10  # 10=智能表格
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")


def create_fields_from_mappings(field_mappings: List[Dict]) -> List[Dict]:
    """
    根据字段映射创建API字段配置，反转顺序以匹配CSV列顺序
    """
    # 确保所有字段映射都有column_index
    for i, mapping in enumerate(field_mappings):
        if "column_index" not in mapping:
            mapping["column_index"] = i
    
    # 按column_index排序，然后反转顺序
    sorted_mappings = sorted(field_mappings, key=lambda x: x["column_index"])
    reversed_mappings = list(reversed(sorted_mappings))  # 反转顺序
    
    fields = []
    for mapping in reversed_mappings:
        field_config = {
            "field_title": mapping["field_title"],
            "field_type": mapping["field_type"]
        }
        
        # 添加特定类型的属性
        if mapping["field_type"] == "FIELD_TYPE_NUMBER" and "property_number" in mapping:
            field_config["property_number"] = mapping["property_number"]
        elif mapping["field_type"] == "FIELD_TYPE_SINGLE_SELECT" and "property_single_select" in mapping:
            field_config["property_single_select"] = mapping["property_single_select"]
        elif mapping["field_type"] == "FIELD_TYPE_SELECT" and "property_select" in mapping:
            field_config["property_select"] = mapping["property_select"]
        elif mapping["field_type"] == "FIELD_TYPE_USER" and "property_user" in mapping:
            field_config["property_user"] = mapping["property_user"]
        elif mapping["field_type"] == "FIELD_TYPE_DATE_TIME" and "property_date_time" in mapping:
            field_config["property_date_time"] = mapping["property_date_time"]
        
        fields.append(field_config)
    
    return fields


def send_wecom_text_message(access_token, agentid, touser, content):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
    data = {
        "touser": touser,  # "@all" 表示全体成员
        "msgtype": "text",
        "agentid": agentid,
        "text": {
            "content": content
        },
        "safe": 0
    }
    resp = requests.post(url, json=data)
    return resp.json()


def upload_csv_to_wxwork(csv_file_path, corpid, corpsecret, prev_docid=None):
    """处理CSV文件并上传到企业微信，支持传递上一次文档ID"""
    
    # 1. 获取访问令牌
    access_token = get_access_token(corpid, corpsecret)
    print(f"✅ 获取访问令牌成功")
    
    # 2. 使用现有的CSV处理器
    processor = CSVDataProcessor()
    
    # 3. 使用extract_problems_from_csv方法提取问题数据（按借据号去重）
    print(f"📖 开始解析CSV文件: {csv_file_path}")
    completeness_problems, accuracy_problems, df = processor.extract_problems_from_csv(csv_file_path)
    
    # 4. 统计信息
    total_failed_bills = len(df[df['approval_result'] == '不通过']['bill_no'].unique())
    total_records = len(df)
    print(f"✅ 成功解析 {total_records} 条原始记录，{total_failed_bills} 个不通过的借据号")
    print(f"📊 问题统计:")
    print(f"   完备性问题: {len(completeness_problems)} 条")
    print(f"   准确性问题: {len(accuracy_problems)} 条")
    
    # 新增：如果所有记录都为通过，则不创建文档
    if total_failed_bills == 0:
        print(f"🎉 所有记录均为通过，无需创建企业微信文档。")
        return {
            "success": False,
            "reason": "全部通过，无需创建文档",
            "total_records": total_records,
            "total_failed_bills": total_failed_bills
        }
    
    # 5. 创建企业微信文档
    doc_title = f"CSV完整报告_{csv_file_path}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, docurl = create_document(access_token, doc_title)
    print(f"📄 创建文档成功，docid: {docid}")
    print(f"🔗 文档链接: {docurl}")
    
    # 新增：推送文档链接到企业微信全体成员
    agentid = 1000002
    touser = "@all"
    msg = f"新上传的文档已创建，点击查看：{docurl}"
    send_result = send_wecom_text_message(access_token, agentid, touser, msg)
    print(f"📢 已推送文档链接到企业微信，返回: {send_result}")
    
    # 5.1 记录上一次文档ID（如有）
    prev_docid_field = None
    if prev_docid:
        prev_docid_field = {"column_index": len(df.columns), "field_title": "上次文档ID", "field_type": "FIELD_TYPE_TEXT"}
    
    # 6. 使用现有的企业微信API客户端
    client = WxworkSmartsheetApiClient(access_token)
    
    # ========== 第一个表格：原始数据 ==========
    print(f"\n📋 开始创建原始数据表格...")
    
    # 7. 创建原始数据子表
    original_sheet_title = "原始数据"
    add_original_sheet_result = client.add_sheet(docid=docid, title=original_sheet_title, index=0)
    original_sheet_id = add_original_sheet_result.get("properties", {}).get("sheet_id")
    print(f"📋 创建原始数据子表成功，sheet_id: {original_sheet_id}")
    
    # 8. 定义原始数据字段映射（动态获取CSV的所有列）
    original_field_mappings = []
    for i, column in enumerate(df.columns):
        original_field_mappings.append({
            "column_index": i,
            "field_title": column,
            "field_type": "FIELD_TYPE_TEXT"
        })
    
    # 如果有上次文档ID，插入到字段映射
    if prev_docid_field:
        original_field_mappings.append(prev_docid_field)
    
    # 9. 创建原始数据字段配置（反转顺序）
    original_fields = create_fields_from_mappings(original_field_mappings)
    print(f"📝 添加原始数据字段成功，字段数量: {len(original_fields)}")
    print(f"   字段顺序: {[f['field_title'] for f in original_fields]}")
    
    client.add_fields(docid=docid, sheet_id=original_sheet_id, fields=original_fields)
    
    # 10. 准备原始数据上传（包含所有列）
    original_records = []
    for _, row in df.iterrows():
        record_values = {}
        for field in original_fields:
            field_title = field['field_title']
            if field_title == "上次文档ID" and prev_docid:
                value = prev_docid
            else:
                value = str(row.get(field_title, ''))
            record_values[field_title] = [{"type": "text", "text": value}]
        
        record_data = {"values": record_values}
        original_records.append(record_data)
    
    # 11. 分批上传原始数据
    batch_size = 100
    original_uploaded = 0
    
    for i in range(0, len(original_records), batch_size):
        batch_records = original_records[i:i + batch_size]
        add_records_result = client.add_records(
            docid=docid, 
            sheet_id=original_sheet_id, 
            key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
            records=batch_records
        )
        
        batch_count = len(add_records_result.get("records", []))
        original_uploaded += batch_count
        print(f"📤 上传原始数据第 {i//batch_size + 1} 批，{batch_count} 条记录")
        
        # 避免频率限制
        time.sleep(0.5)
    
    print(f"✅ 原始数据上传完成，总共上传 {original_uploaded} 条记录")
    
    # ========== 第二个表格：问题汇总 ==========
    print(f"\n📋 开始创建问题汇总表格...")
    
    # 12. 创建问题汇总子表
    problems_sheet_title = "问题汇总"
    add_problems_sheet_result = client.add_sheet(docid=docid, title=problems_sheet_title, index=1)
    problems_sheet_id = add_problems_sheet_result.get("properties", {}).get("sheet_id")
    print(f"📋 创建问题汇总子表成功，sheet_id: {problems_sheet_id}")
    
    # 13. 定义问题汇总字段映射
    problems_field_mappings = [
        {"column_index": 0, "field_title": "借据号", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 1, "field_title": "问题类型", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 2, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 3, "field_title": "处理时间", "field_type": "FIELD_TYPE_TEXT"}
    ]
    
    # 14. 创建问题汇总字段配置（反转顺序）
    problems_fields = create_fields_from_mappings(problems_field_mappings)
    print(f"📝 添加问题汇总字段成功，字段顺序: {[f['field_title'] for f in problems_fields]}")
    
    client.add_fields(docid=docid, sheet_id=problems_sheet_id, fields=problems_fields)
    
    # 15. 准备问题汇总数据上传
    problems_records = []
    current_time = datetime.now().isoformat()
    
    # 添加完备性问题
    for bill_no, detail in completeness_problems:
        record_data = {
            "values": {
                "处理时间": [{"type": "text", "text": current_time}],
                "问题详情": [{"type": "text", "text": detail}],
                "问题类型": [{"type": "text", "text": "完备性"}],
                "借据号": [{"type": "text", "text": bill_no}]
            }
        }
        problems_records.append(record_data)
    
    # 添加准确性问题
    for bill_no, detail in accuracy_problems:
        record_data = {
            "values": {
                "处理时间": [{"type": "text", "text": current_time}],
                "问题详情": [{"type": "text", "text": detail}],
                "问题类型": [{"type": "text", "text": "准确性"}],
                "借据号": [{"type": "text", "text": bill_no}]
            }
        }
        problems_records.append(record_data)
    
    # 16. 分批上传问题汇总数据
    problems_uploaded = 0
    
    if problems_records:
        for i in range(0, len(problems_records), batch_size):
            batch_records = problems_records[i:i + batch_size]
            add_records_result = client.add_records(
                docid=docid, 
                sheet_id=problems_sheet_id, 
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
                records=batch_records
            )
            
            batch_count = len(add_records_result.get("records", []))
            problems_uploaded += batch_count
            print(f"📤 上传问题汇总第 {i//batch_size + 1} 批，{batch_count} 条记录")
            
            # 避免频率限制
            time.sleep(0.5)
    else:
        print(f"⚠️ 没有找到问题数据，跳过问题汇总上传")
    
    print(f"✅ 问题汇总上传完成，总共上传 {problems_uploaded} 条记录")
    
    # ========== 第三个表格：完备性问题 ==========
    print(f"\n📋 开始创建完备性问题表格...")
    
    # 17. 创建完备性问题子表
    completeness_sheet_title = "完备性问题"
    add_completeness_sheet_result = client.add_sheet(docid=docid, title=completeness_sheet_title, index=2)
    completeness_sheet_id = add_completeness_sheet_result.get("properties", {}).get("sheet_id")
    print(f"📋 创建完备性问题子表成功，sheet_id: {completeness_sheet_id}")
    
    # 18. 定义完备性问题字段映射（使用企业微信默认字段）
    completeness_field_mappings = [
        {"column_index": 0, "field_title": "借据号", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 1, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 2, "field_title": "处理时间", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 3, "field_title": "处理人员", "field_type": "FIELD_TYPE_USER", 
         "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 4, "field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [{"text": "未开始"}, {"text": "进行中"}, {"text": "已完成"}]}},
        {"column_index": 5, "field_title": "截止时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}},
        {"column_index": 6, "field_title": "OCR人员", "field_type": "FIELD_TYPE_USER", 
         "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 7, "field_title": "重新OCR状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [{"text": "未开始"}, {"text": "进行中"}, {"text": "已完成"}]}},
    ]
    
    # 19. 创建完备性问题字段配置（反转顺序）
    completeness_fields = create_fields_from_mappings(completeness_field_mappings)
    print(f"📝 添加完备性问题字段成功，字段顺序: {[f['field_title'] for f in completeness_fields]}")
    
    # 添加调试信息：打印字段定义
    print(f"🔍 调试：完备性问题字段定义: {completeness_fields}")
    
    add_fields_result = client.add_fields(docid=docid, sheet_id=completeness_sheet_id, fields=completeness_fields)
    print(f"🔍 调试：add_fields返回结果: {add_fields_result}")
    
    # 20. 从问题汇总表拆分完备性问题数据上传
    completeness_records = []
    dev_users = USERS.get("开发人员", [])
    dev_user_objs = [{"type": "user", "user_id": u["user_id"]} for u in dev_users]
    
    # 计算截止时间（处理时间 + 10分钟）
    current_time = datetime.now()
    deadline_time = current_time + timedelta(minutes=DEADLINE_MINUTES)
    deadline_timestamp = int(deadline_time.timestamp())
    deadline_formatted = deadline_time.strftime("%Y-%m-%d %H:%M")
    
    print(f"🔍 调试：当前时间: {current_time}")
    print(f"🔍 调试：截止时间: {deadline_time}")
    print(f"🔍 调试：截止时间戳: {deadline_timestamp}")
    print(f"🔍 调试：截止时间格式化: {deadline_formatted}")
    
    for record in problems_records:
        values = record["values"]
        problem_type = values.get("问题类型", [{}])[0].get("text", "")
        if problem_type == "完备性":
            # 处理时间字段转换为YYYY-MM-DD
            raw_time = values.get("处理时间", [{}])[0].get("text", "")
            date_time_val = raw_time.split("T")[0] if "T" in raw_time else raw_time
            # 问题详情字段转为纯文本
            raw_detail = values.get("问题详情", [{}])[0].get("text", "")
            try:
                parsed_detail = json.loads(raw_detail)
                if isinstance(parsed_detail, list):
                    detail_text = "、".join(parsed_detail)
                else:
                    detail_text = raw_detail
            except Exception:
                detail_text = raw_detail
            completeness_records.append({
                "values": {
                    "任务状态": [{"type": "single_select", "text": "未开始"}],
                    "处理人员": dev_user_objs,
                    "处理时间": [{"type": "text", "text": date_time_val}],
                    "问题详情": [{"type": "text", "text": detail_text}],
                    "借据号": values.get("借据号", []),
                    "截止时间": [{"type": "datetime", "datetime": deadline_timestamp}],
                    "OCR人员": [],
                    "重新OCR状态": []
                }
            })

    print(f"🔍 调试：筛选出完备性问题 {len(completeness_records)} 条")
    if completeness_records:
        print(f"🔍 调试：完备性问题第一条记录: {completeness_records[0]}")
    
    # 21. 分批上传完备性问题数据
    completeness_uploaded = 0
    
    if completeness_records:
        for i in range(0, len(completeness_records), batch_size):
            batch_records = completeness_records[i:i + batch_size]
            add_records_result = client.add_records(
                docid=docid, 
                sheet_id=completeness_sheet_id, 
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
                records=batch_records
            )
            
            batch_count = len(add_records_result.get("records", []))
            completeness_uploaded += batch_count
            print(f"📤 上传完备性问题第 {i//batch_size + 1} 批，{batch_count} 条记录")
            
            # 避免频率限制
            time.sleep(0.5)
    else:
        print(f"⚠️ 没有找到完备性问题数据，跳过完备性问题上传")
    
    print(f"✅ 完备性问题上传完成，总共上传 {completeness_uploaded} 条记录")
    
    # ========== 第四个表格：准确性问题 ==========
    print(f"\n📋 开始创建准确性问题表格...")
    
    # 22. 创建准确性问题子表
    accuracy_sheet_title = "准确性问题"
    add_accuracy_sheet_result = client.add_sheet(docid=docid, title=accuracy_sheet_title, index=3)
    accuracy_sheet_id = add_accuracy_sheet_result.get("properties", {}).get("sheet_id")
    print(f"📋 创建准确性问题子表成功，sheet_id: {accuracy_sheet_id}")
    
    # 23. 定义准确性问题字段映射（使用企业微信默认字段）
    accuracy_field_mappings = [
        {"column_index": 0, "field_title": "借据号", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 1, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 2, "field_title": "处理时间", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 3, "field_title": "开发人员", "field_type": "FIELD_TYPE_USER", 
         "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 4, "field_title": "任务原因", "field_type": "FIELD_TYPE_SELECT", 
         "property_select": {"options": [{"text": "影像件模糊"}, {"text": "内容错误"}, {"text": "系统问题"}]}},
        {"column_index": 5, "field_title": "任务名称", "field_type": "FIELD_TYPE_SELECT", 
         "property_select": {"options": [{"text": "调整ocr通过阈值"}, {"text": "与合作机构沟通核对并补传"}, {"text": "修复系统问题"}]}},
        {"column_index": 6, "field_title": "处理人员", "field_type": "FIELD_TYPE_USER", 
         "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 7, "field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [{"text": "未开始"}, {"text": "进行中"}, {"text": "已完成"}]}},
        {"column_index": 8, "field_title": "截止时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}},
        {"column_index": 9, "field_title": "OCR人员", "field_type": "FIELD_TYPE_USER", 
         "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 10, "field_title": "重新OCR状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [{"text": "未开始"}, {"text": "进行中"}, {"text": "已完成"}]}},
    ]
    
    
    # 24. 创建准确性问题字段配置（反转顺序）
    accuracy_fields = create_fields_from_mappings(accuracy_field_mappings)
    print(f"📝 添加准确性问题字段成功，字段顺序: {[f['field_title'] for f in accuracy_fields]}")
    
    # 添加调试信息：打印字段定义
    print(f"🔍 调试：准确性问题字段定义: {accuracy_fields}")
    
    add_fields_result = client.add_fields(docid=docid, sheet_id=accuracy_sheet_id, fields=accuracy_fields)
    print(f"🔍 调试：add_fields返回结果: {add_fields_result}")
    
    # 25. 从问题汇总表拆分准确性问题数据上传
    accuracy_records = []
    dev_users = USERS.get("开发人员", [])
    dev_user_objs = [{"type": "user", "user_id": u["user_id"]} for u in dev_users]
    
    # 计算截止时间（处理时间 + 10分钟）
    current_time = datetime.now()
    deadline_time = current_time + timedelta(minutes=DEADLINE_MINUTES)
    deadline_timestamp = int(deadline_time.timestamp())
    deadline_formatted = deadline_time.strftime("%Y-%m-%d %H:%M")
    
    print(f"🔍 调试准确性：当前时间: {current_time}")
    print(f"🔍 调试准确性：截止时间: {deadline_time}")
    print(f"🔍 调试准确性：截止时间戳: {deadline_timestamp}")
    print(f"🔍 调试准确性：截止时间格式化: {deadline_formatted}")
    
    for record in problems_records:
        values = record["values"]
        problem_type = values.get("问题类型", [{}])[0].get("text", "")
        if problem_type == "准确性":
            # 处理时间字段转换为YYYY-MM-DD
            raw_time = values.get("处理时间", [{}])[0].get("text", "")
            date_time_val = raw_time.split("T")[0] if "T" in raw_time else raw_time
            # 问题详情字段转为纯文本
            raw_detail = values.get("问题详情", [{}])[0].get("text", "")
            try:
                parsed_detail = json.loads(raw_detail)
                if isinstance(parsed_detail, list):
                    detail_text = "、".join(parsed_detail)
                else:
                    detail_text = raw_detail
            except Exception:
                detail_text = raw_detail
            accuracy_records.append({
                "values": {
                    "任务状态": [{"type": "single_select", "text": "未开始"}],
                    "截止时间": [{"type": "datetime", "datetime": deadline_timestamp}],
                    "开发人员": dev_user_objs,
                    "处理人员": [],
                    "处理时间": [{"type": "text", "text": date_time_val}],
                    "问题详情": [{"type": "text", "text": detail_text}],
                    "借据号": values.get("借据号", [])
                }
            })

    print(f"🔍 调试：筛选出准确性问题 {len(accuracy_records)} 条")
    if accuracy_records:
        print(f"🔍 调试：准确性问题第一条记录: {accuracy_records[0]}")
    
    # 26. 分批上传准确性问题数据
    accuracy_uploaded = 0
    
    if accuracy_records:
        for i in range(0, len(accuracy_records), batch_size):
            batch_records = accuracy_records[i:i + batch_size]
            add_records_result = client.add_records(
                docid=docid, 
                sheet_id=accuracy_sheet_id, 
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
                records=batch_records
            )
            
            batch_count = len(add_records_result.get("records", []))
            accuracy_uploaded += batch_count
            print(f"📤 上传准确性问题第 {i//batch_size + 1} 批，{batch_count} 条记录")
            
            # 避免频率限制
            time.sleep(0.5)
    else:
        print(f"⚠️ 没有找到准确性问题数据，跳过准确性问题上传")
    
    print(f"✅ 准确性问题上传完成，总共上传 {accuracy_uploaded} 条记录")
    
    return {
        "success": True,
        "docid": docid,
        "docurl": docurl,
        "original_sheet_id": original_sheet_id,
        "problems_sheet_id": problems_sheet_id,
        "completeness_sheet_id": completeness_sheet_id,
        "accuracy_sheet_id": accuracy_sheet_id,
        "total_records": total_records,
        "total_failed_bills": total_failed_bills,
        "completeness_problems": len(completeness_problems),
        "accuracy_problems": len(accuracy_problems),
        "original_uploaded": original_uploaded,
        "problems_uploaded": problems_uploaded,
        "completeness_uploaded": completeness_uploaded,
        "accuracy_uploaded": accuracy_uploaded,
        "access_token": access_token
    }


def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)
    # 企业微信配置
    corpid = CORPID
    corpsecret = CORPSECRET
    # 要处理的CSV文件列表
    csv_files = CSV_FILES
    for csv_file in csv_files:
        try:
            print(f"\n{'='*60}")
            print(f"🚀 开始处理文件: {csv_file}")
            print(f"{'='*60}")
            result = upload_csv_to_wxwork(csv_file, corpid, corpsecret)
            if result["success"]:
                print(f"\n🎉 处理成功!")
                print(f"   文件名: {csv_file}")
                print(f"   文档ID: {result['docid']}")
                print(f"   文档链接: {result['docurl']}")
                print(f"   原始数据子表ID: {result['original_sheet_id']}")
                print(f"   问题汇总子表ID: {result['problems_sheet_id']}")
                print(f"   完备性问题子表ID: {result['completeness_sheet_id']}")
                print(f"   准确性问题子表ID: {result['accuracy_sheet_id']}")
                print(f"   总记录数: {result['total_records']}")
                print(f"   总失败借据数: {result['total_failed_bills']}")
                print(f"   完备性问题数: {result['completeness_problems']}")
                print(f"   准确性问题数: {result['accuracy_problems']}")
                print(f"   原始数据上传记录数: {result['original_uploaded']}")
                print(f"   问题汇总上传记录数: {result['problems_uploaded']}")
                print(f"   完备性问题上传记录数: {result['completeness_uploaded']}")
                print(f"   准确性问题上传记录数: {result['accuracy_uploaded']}")
            else:
                print(f"✅ {csv_file} 全部通过，无需创建企业微信文档。")
        except Exception as e:
            print(f"❌ 处理文件 {csv_file} 失败: {e}")
    print(f"\n{'='*60}")
    print("🏁 所有文件处理完成")
    print(f"{'='*60}")


def process_single_csv(csv_path):
    corpid = CORPID
    corpsecret = CORPSECRET
    result = upload_csv_to_wxwork(csv_path, corpid, corpsecret)
    # 可选：打印或记录处理结果
    return result


if __name__ == "__main__":
    main() 