#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试进度字段的脚本
专门验证当前代码中进度字段格式的问题
"""

import json
import requests
import time
from wxwork_smartsheet_api_client import WxworkSmartsheetApiClient

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

CORPID = config['corpid']
CORPSECRET = config['corpsecret']

def get_access_token(corpid, corpsecret):
    """获取企业微信访问令牌"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    response = requests.get(url)
    result = response.json()
    if result.get('errcode') == 0:
        return result.get('access_token')
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_test_document(access_token):
    """创建测试文档"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_type": 4,  # smartsheet类型
        "doc_name": f"进度字段快速测试_{int(time.time())}"
    }
    response = requests.post(url, json=data)
    result = response.json()
    if result.get('errcode') == 0:
        return result.get('docid'), result.get('doc_url')
    else:
        raise Exception(f"创建文档失败: {result}")

def quick_test_progress_formats():
    """快速测试当前代码的进度字段格式问题"""
    print("🚀 快速测试进度字段格式...")
    
    # 获取访问令牌
    access_token = get_access_token(CORPID, CORPSECRET)
    print("✅ 访问令牌获取成功")
    
    # 创建测试文档
    docid, doc_url = create_test_document(access_token)
    print(f"✅ 测试文档创建成功: {doc_url}")
    
    # 获取默认子表
    client = WxworkSmartsheetApiClient(access_token)
    sheets_result = client.get_sheets(docid)
    sheet_id = sheets_result['sheets'][0]['sheet_id']
    
    # 添加字段（模拟原代码的字段结构）
    fields = [
        {"field_title": "任务名称", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [
             {"text": "未开始", "style": 18},
             {"text": "进行中", "style": 12},
             {"text": "已完成", "style": 16}
         ]}},
        {"field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", 
         "property_progress": {"decimal_places": 0}}
    ]
    
    add_fields_result = client.add_fields(docid, sheet_id, fields)
    print(f"✅ 字段添加结果: {add_fields_result}")
    
    # 测试当前代码的格式（可能有问题的格式）
    print("\n🔍 测试当前代码使用的格式...")
    current_format_records = [
        {
            "values": {
                "任务名称": [{"type": "text", "text": "当前代码格式测试"}],
                "任务状态": [{"type": "single_select", "text": "未开始"}],
                "进度": [{"type": "number", "number": 0}]  # 当前代码的格式
            }
        }
    ]
    
    try:
        result1 = client.add_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", current_format_records)
        print(f"✅ 当前格式添加成功: {result1}")
        current_record_id = result1.get('records', [{}])[0].get('record_id')
    except Exception as e:
        print(f"❌ 当前格式添加失败: {e}")
        current_record_id = None
    
    # 测试API文档建议的格式
    print("\n🔍 测试API文档建议的格式...")
    api_format_records = [
        {
            "values": {
                "任务名称": [{"type": "text", "text": "API文档格式测试"}],
                "任务状态": [{"type": "single_select", "text": "进行中"}],
                "进度": 50  # API文档建议的格式
            }
        }
    ]
    
    try:
        result2 = client.add_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", api_format_records)
        print(f"✅ API格式添加成功: {result2}")
        api_record_id = result2.get('records', [{}])[0].get('record_id')
    except Exception as e:
        print(f"❌ API格式添加失败: {e}")
        api_record_id = None
    
    # 读取并比较结果
    print("\n📊 读取记录并比较结果...")
    time.sleep(1)
    all_records = client.get_records(docid, sheet_id)
    
    for record in all_records:
        values = record.get('values', {})
        task_name = values.get('任务名称', [{}])[0].get('text', '未知')
        progress = values.get('进度', '未设置')
        
        print(f"\n记录: {task_name}")
        print(f"  进度字段值: {progress}")
        print(f"  进度字段类型: {type(progress)}")
        
        # 检查进度是否正确显示
        if isinstance(progress, (int, float)):
            print(f"  ✅ 进度值为数值类型，应该能正常显示")
        elif isinstance(progress, list) and progress:
            print(f"  ⚠️ 进度值为列表类型，可能显示异常")
        else:
            print(f"  ❌ 进度值格式异常")
    
    # 测试更新操作
    print("\n🔄 测试进度字段更新...")
    
    if current_record_id:
        print("测试当前代码格式的更新...")
        try:
            # 模拟监控代码中的更新逻辑
            update_result1 = client.update_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", [
                {
                    "record_id": current_record_id,
                    "values": {"进度": {"type": "number", "number": 75}}  # 当前代码的更新格式
                }
            ])
            print(f"✅ 当前格式更新成功: {update_result1}")
        except Exception as e:
            print(f"❌ 当前格式更新失败: {e}")
    
    if api_record_id:
        print("测试API文档格式的更新...")
        try:
            update_result2 = client.update_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", [
                {
                    "record_id": api_record_id,
                    "values": {"进度": 85}  # API文档建议的更新格式
                }
            ])
            print(f"✅ API格式更新成功: {update_result2}")
        except Exception as e:
            print(f"❌ API格式更新失败: {e}")
    
    # 最终读取验证
    print("\n🔍 最终验证更新结果...")
    time.sleep(1)
    final_records = client.get_records(docid, sheet_id)
    
    for record in final_records:
        values = record.get('values', {})
        task_name = values.get('任务名称', [{}])[0].get('text', '未知')
        progress = values.get('进度', '未设置')
        
        print(f"\n最终记录: {task_name}")
        print(f"  最终进度值: {progress}")
    
    print(f"\n🎯 测试完成！")
    print(f"📋 测试文档链接: {doc_url}")
    print(f"\n💡 结论:")
    print(f"   1. 请在企业微信中查看进度字段是否正常显示")
    print(f"   2. 对比两种格式的显示效果")
    print(f"   3. 如果API文档格式显示正常，建议修改原代码")
    
    return docid, doc_url

def simulate_monitor_update():
    """模拟监控代码中的进度更新逻辑"""
    print("\n🔄 模拟监控代码的进度更新逻辑...")
    
    # 模拟任务状态变化对应的进度值
    status_progress_map = {
        "未开始": 0,
        "进行中": 50,
        "已完成": 100
    }
    
    access_token = get_access_token(CORPID, CORPSECRET)
    docid, doc_url = create_test_document(access_token)
    client = WxworkSmartsheetApiClient(access_token)
    
    sheets_result = client.get_sheets(docid)
    sheet_id = sheets_result['sheets'][0]['sheet_id']
    
    # 添加字段
    fields = [
        {"field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [
             {"text": "未开始", "style": 18},
             {"text": "进行中", "style": 12},
             {"text": "已完成", "style": 16}
         ]}},
        {"field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", 
         "property_progress": {"decimal_places": 0}}
    ]
    client.add_fields(docid, sheet_id, fields)
    
    # 创建初始记录
    initial_record = [{
        "values": {
            "任务状态": [{"type": "single_select", "text": "未开始"}],
            "进度": 0  # 使用API文档格式
        }
    }]
    
    add_result = client.add_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", initial_record)
    record_id = add_result['records'][0]['record_id']
    
    print(f"✅ 创建初始记录，ID: {record_id}")
    
    # 模拟状态变化和进度更新
    for status, expected_progress in status_progress_map.items():
        print(f"\n🔄 更新状态为: {status}, 期望进度: {expected_progress}")
        
        # 测试当前代码的更新方式
        try:
            update_values_old = {
                "任务状态": [{"type": "single_select", "text": status}],
                "进度": {"type": "number", "number": expected_progress}  # 当前代码格式
            }
            
            result_old = client.update_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", [
                {"record_id": record_id, "values": update_values_old}
            ])
            print(f"  当前格式更新结果: {result_old}")
        except Exception as e:
            print(f"  ❌ 当前格式更新失败: {e}")
        
        time.sleep(1)
        
        # 测试API文档建议的更新方式
        try:
            update_values_new = {
                "任务状态": [{"type": "single_select", "text": status}],
                "进度": expected_progress  # API文档格式
            }
            
            result_new = client.update_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", [
                {"record_id": record_id, "values": update_values_new}
            ])
            print(f"  API格式更新结果: {result_new}")
        except Exception as e:
            print(f"  ❌ API格式更新失败: {e}")
        
        # 读取当前值
        time.sleep(1)
        records = client.get_records(docid, sheet_id)
        for record in records:
            if record['record_id'] == record_id:
                current_progress = record.get('values', {}).get('进度', '未找到')
                print(f"  当前进度值: {current_progress}")
                break
        
        time.sleep(2)  # 等待下次更新
    
    print(f"\n📋 监控模拟测试文档: {doc_url}")
    return doc_url

if __name__ == "__main__":
    print("⚡ 进度字段快速测试工具")
    print("=" * 40)
    
    try:
        # 基础格式测试
        print("\n【测试1】基础格式对比测试")
        docid, doc_url = quick_test_progress_formats()
        
        print("\n" + "=" * 40)
        
        # 监控逻辑模拟测试
        print("\n【测试2】监控逻辑模拟测试")
        monitor_url = simulate_monitor_update()
        
        print(f"\n🎯 快速测试完成！")
        print(f"   基础测试文档: {doc_url}")
        print(f"   监控测试文档: {monitor_url}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
