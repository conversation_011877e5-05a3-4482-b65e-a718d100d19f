#!/usr/bin/env python3
# -*- coding: utf-8-*-

测试企业微信智能表格进度字段功能
用于验证进度字段的创建、读取和更新是否正常工作import json
import requests
import logging
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s"
)

# 从配置文件读取配置
try:
    with open('config.json,r, encoding='utf-8 f:
        config = json.load(f)
    CORPID = config["corpid"]
    CORPSECRET = config["corpsecret"]
except Exception as e:
    logging.error(f"读取配置文件失败: {e}")
    CORPID =your_corpid    CORPSECRET = "your_corpsecret"

def get_access_token(corpid, corpsecret):
   获取企业微信访问令牌"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") ==0     return result.get("access_token")
    else:
        raise Exception(f获取访问令牌失败: {result}")

def create_document(access_token, title):
   创建企业微信文档，返回docid和url"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
       doc_name": title,
        doc_type":10  #10能表格
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") ==0     return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result})

def add_sheet(access_token, docid, title, index=0):
    添加子表"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    data = [object Object]    docid": docid,
      title": title,
        index: index
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") ==0     return result
    else:
        raise Exception(f"添加子表失败: {result})

def add_fields(access_token, docid, sheet_id, fields):
    添加字段"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
    data = [object Object]    docid": docid,
       sheet_id": sheet_id,
       fields": fields
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") ==0     return result
    else:
        raise Exception(f"添加字段失败: {result}")

def add_records(access_token, docid, sheet_id, records):
    添加记录"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
    data = [object Object]    docid": docid,
       sheet_id": sheet_id,
       key_type: ELL_VALUE_KEY_TYPE_FIELD_TITLE",
        records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") ==0     return result
    else:
        raise Exception(f"添加记录失败: {result}")

def get_records(access_token, docid, sheet_id):
    获取记录"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={access_token}"
    params = [object Object]    docid": docid,
       sheet_id:sheet_id
    }
    resp = requests.get(url, params=params)
    result = resp.json()
    if result.get("errcode") ==0     return result.get(records", [])
    else:
        raise Exception(f"获取记录失败: {result})def update_records(access_token, docid, sheet_id, records):
    更新记录"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/update_records?access_token={access_token}"
    data = [object Object]    docid": docid,
       sheet_id": sheet_id,
       key_type: ELL_VALUE_KEY_TYPE_FIELD_TITLE",
        records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") ==0     return result
    else:
        raise Exception(f"更新记录失败: {result}")

def test_progress_field():
    段功能""    logging.info("🚀 开始测试进度字段功能...")
    
    try:
        #1问令牌
        access_token = get_access_token(CORPID, CORPSECRET)
        logging.info("✅ 获取访问令牌成功")
        
        #2创建测试文档
        doc_title = f"进度字段测试_{datetime.now().strftime('%Y%m%d_%H%M%S)}"
        docid, docurl = create_document(access_token, doc_title)
        logging.info(f✅ 创建测试文档成功，docid: {docid})      logging.info(f"🔗 文档链接: {docurl}")
        
        # 3. 添加子表
        sheet_result = add_sheet(access_token, docid, "进度测试表", 0)
        sheet_id = sheet_result.get(properties",[object Object]).get("sheet_id)      logging.info(f✅ 添加子表成功，sheet_id: {sheet_id}")
        
        # 4. 添加字段（包含进度字段）
        fields = [
           [object Object]
                field_title": "任务名称,
             field_type": FIELD_TYPE_TEXT"
            },
           [object Object]
                field_title": "任务状态,
             field_type": FIELD_TYPE_SINGLE_SELECT,
              property_single_select": {
               options                   {"text: 未开始", "style": 18},
                        {"text: 进行中", "style": 12},
                        {"text: 已完成", "style": 16}
                    ]
                }
            },
           [object Object]
                field_title": "进度,
             field_type:FIELD_TYPE_PROGRESS,
                property_progress": {
                    decimal_places":0                }
            }
        ]
        
        add_fields_result = add_fields(access_token, docid, sheet_id, fields)
        logging.info(f"✅ 添加字段成功: {add_fields_result}")
        
        # 5. 测试不同的进度字段格式
        test_records = []
        
        # 测试格式1 使用 number 类型（当前代码使用的格式）
        test_records.append({
      values[object Object]
               任务名称:{"type:text", "text": "测试任务1"}],
               任务状态": [{"type": single_select",text],
               进度{"type":number", "number:0         }
        })
        
        # 测试格式2: progress 类型
        test_records.append({
      values[object Object]
               任务名称:{"type:text", "text": "测试任务2"}],
               任务状态": [{"type": single_select",text],
               进度{"type": "progress,progress: 50         }
        })
        
        # 测试格式3: 使用 double 类型
        test_records.append({
      values[object Object]
               任务名称:{"type:text", "text": "测试任务3"}],
               任务状态": [{"type": single_select",text],
               进度{"type":double", "double":100         }
        })
        
        # 测试格式4: 直接使用数值
        test_records.append({
      values[object Object]
               任务名称:{"type:text", "text": "测试任务4"}],
               任务状态": [{"type": single_select",text],
       进度":75         }
        })
        
        #6添加测试记录
        add_records_result = add_records(access_token, docid, sheet_id, test_records)
        logging.info(f"✅ 添加测试记录成功: {add_records_result}")
        
        #7. 获取记录并查看进度字段的实际格式
        records = get_records(access_token, docid, sheet_id)
        logging.info(f"📋 获取到 {len(records)} 条记录")
        
        for i, record in enumerate(records):
            record_id = record.get("record_id")
            values = record.get("values, {})
            task_name = values.get("任务名称", [{}])[0].get("text, )
            task_status = values.get("任务状态", [{}])[0].get("text,           progress = values.get("进度",      
            logging.info(f"📝 记录 {i+1}:")
            logging.info(f"   record_id: {record_id}")
            logging.info(f"   任务名称: {task_name}")
            logging.info(f"   任务状态: {task_status}")
            logging.info(f"   进度字段原始值: {progress}")
            
            # 分析进度字段的格式
            if progress:
                progress_value = progress[0           logging.info(f   进度字段类型: {type(progress_value)})           logging.info(f"   进度字段内容: {progress_value}")
                
                # 尝试提取数值
                if isinstance(progress_value, dict):
                    for key, value in progress_value.items():
                        logging.info(f"     [object Object]key}: {value} (类型: {type(value)}))              else:
                    logging.info(f   直接值: {progress_value}")
        
        # 8. 测试更新进度字段
        if records:
            first_record = records[0]
            record_id = first_record.get("record_id")
            
            logging.info(f🔄 测试更新记录 {record_id} 的进度字段...")
            
            # 测试不同的更新格式
            update_formats =
                # 格式1: number类型
                {"type":number, },
                # 格式2: progress类型
                {"type": "progress",progress},
                # 格式3: double类型
                {"type":double, },
                # 格式4: 直接数值
                100   ]
            
            for i, progress_format in enumerate(update_formats):
                try:
                    update_data = {
                  record_id": record_id,
                  values                进度: [progress_format]
                        }
                    }
                    
                    update_result = update_records(access_token, docid, sheet_id, [update_data])
                    logging.info(f"✅ 更新格式 {i+1} 成功: {progress_format}")
                    
                    # 等待一下再获取更新后的记录
                    time.sleep(1)
                    updated_records = get_records(access_token, docid, sheet_id)
                    updated_record = next((r for r in updated_records if r.get("record_id") == record_id), None)
                    
                    if updated_record:
                        updated_progress = updated_record.get("values", [object Object]                   logging.info(f"   更新后的进度值: {updated_progress}")
                    
                except Exception as e:
                    logging.error(f"❌ 更新格式 {i+1} 失败: {e}")
        
        logging.info("🎉 进度字段测试完成！)      logging.info(f"📄 测试文档链接: {docurl}")
        
        return {
           success True,
          dociddocid,
            docurlocurl,
           sheet_id": sheet_id
        }
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        return {
            successFalse,
          error": str(e)
        }

def test_progress_with_status_update():
   测试根据任务状态自动更新进度""    logging.info(🚀 开始测试任务状态自动更新进度功能...")
    
    try:
        #1问令牌
        access_token = get_access_token(CORPID, CORPSECRET)
        logging.info("✅ 获取访问令牌成功")
        
        #2创建测试文档
        doc_title = f"状态进度联动测试_{datetime.now().strftime('%Y%m%d_%H%M%S)}"
        docid, docurl = create_document(access_token, doc_title)
        logging.info(f✅ 创建测试文档成功，docid: {docid}")
        
        # 3. 添加子表
        sheet_result = add_sheet(access_token, docid, "状态进度联动测试表", 0)
        sheet_id = sheet_result.get(properties",[object Object]).get("sheet_id)      logging.info(f✅ 添加子表成功，sheet_id: {sheet_id}")
        
        # 4. 添加字段
        fields = [
           [object Object]
                field_title": "任务名称,
             field_type": FIELD_TYPE_TEXT"
            },
           [object Object]
                field_title": "任务状态,
             field_type": FIELD_TYPE_SINGLE_SELECT,
              property_single_select": {
               options                   {"text: 未开始", "style": 18},
                        {"text: 进行中", "style": 12},
                        {"text: 已完成", "style": 16}
                    ]
                }
            },
           [object Object]
                field_title": "进度,
             field_type:FIELD_TYPE_PROGRESS,
                property_progress": {
                    decimal_places":0                }
            }
        ]
        
        add_fields(access_token, docid, sheet_id, fields)
        logging.info("✅ 添加字段成功")
        
        #5记录
        initial_record = {
      values[object Object]
               任务名称:{"type:text,text": "自动进度测试任务"}],
               任务状态": [{"type": single_select",text],
               进度{"type":number", "number:0         }
        }
        
        add_records_result = add_records(access_token, docid, sheet_id, [initial_record])
        record_id = add_records_result.get(records", [{}])0.get("record_id)      logging.info(f✅添加初始记录成功，record_id: {record_id}")
        
        # 6. 模拟状态变化并更新进度
        status_progress_map =
            ("未开始,0,
            ("进行中", 50
            ("已完成, 100)       ]
        
        for status, expected_progress in status_progress_map:
            logging.info(f🔄 测试状态: {status} -> 进度: {expected_progress}")
            
            # 更新任务状态
            status_update =[object Object]
          record_id": record_id,
          values                  任务状态": [{"type": single_select,text}]
                }
            }
            
            update_records(access_token, docid, sheet_id, [status_update])
            logging.info(f"✅ 更新状态为: {status}")
            
            # 等待一下
            time.sleep(1)
            
            # 获取更新后的记录
            records = get_records(access_token, docid, sheet_id)
            record = next((r for r in records if r.get("record_id") == record_id), None)
            
            if record:
                current_status = record.get("values,[object Object]}).get("任务状态", [{}])[0].get("text", ")           current_progress = record.get("values", [object Object])           logging.info(f   当前状态: {current_status})           logging.info(f当前进度: {current_progress}")
                
                # 手动更新进度
                progress_update = {
              record_id": record_id,
              values                  进度{"type":number", number": expected_progress}]
                    }
                }
                
                update_records(access_token, docid, sheet_id, [progress_update])
                logging.info(f"✅ 手动更新进度为: {expected_progress}")
                
                # 再次获取记录验证
                time.sleep(1           records = get_records(access_token, docid, sheet_id)
                record = next((r for r in records if r.get("record_id") == record_id), None)
                
                if record:
                    final_progress = record.get("values", [object Object]                   logging.info(f"   最终进度: {final_progress}")
        
        logging.info(🎉 状态进度联动测试完成！)      logging.info(f"📄 测试文档链接: {docurl}")
        
        return {
           success True,
          dociddocid,
            docurlocurl,
           sheet_id": sheet_id
        }
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        return {
            successFalse,
          error": str(e)
        }

if __name__ == "__main__":
    print("🧪 开始测试进度字段功能...)
    print(= * 60)
    
    # 测试1: 基本进度字段功能
    print("\n📋 测试1: 基本进度字段功能")
    result1 = test_progress_field()
    
    print("\n" + = * 60)
    
    # 测试2: 状态进度联动
    print("\n📋 测试2: 状态进度联动测试")
    result2 = test_progress_with_status_update()
    
    print("\n" + "=" * 60)
    print("🏁 所有测试完成！")
    
    if result1["success"]:
        print(f"✅ 测试1文档链接: {result1.get('docurl')}")
    else:
        print(f❌ 测试1失败: {result1('error')}")
    
    if result2["success"]:
        print(f"✅ 测试2文档链接: {result2.get('docurl')}")
    else:
        print(f❌ 测试2失败: {result2 