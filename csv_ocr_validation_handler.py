#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV格式OCR校验数据处理模块
用于解析和处理CSV格式的OCR校验数据，并转换为系统可处理的格式
"""

import json
import csv
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pydantic import BaseModel, Field, validator
import pandas as pd
from collections import defaultdict


class CSVOCRValidationRecord(BaseModel):
    """CSV格式的OCR校验记录模型"""
    bill_no: str = Field(..., description="借据号")
    approval_result: str = Field(..., description="审批结果")
    complete_dtl: Optional[str] = Field(default="", description="完备性详情（JSON数组格式）")
    accuracy_dtl: Optional[str] = Field(default="", description="准确性详情（JSON数组格式）")
    consistency_dtl: Optional[str] = Field(default="", description="一致性详情（JSON数组格式）")
    
    @validator('bill_no')
    def validate_bill_no(cls, v):
        """验证并格式化借据号"""
        # 处理科学计数法格式
        if 'E' in v or 'e' in v:
            try:
                # 转换为整数格式
                bill_number = int(float(v))
                return str(bill_number)
            except (ValueError, OverflowError):
                return v
        return v
    
    @validator('complete_dtl', 'accuracy_dtl', 'consistency_dtl')
    def validate_json_array(cls, v):
        """验证JSON数组格式"""
        if not v or v.strip() == "":
            return ""
        
        try:
            # 尝试解析JSON数组
            parsed = json.loads(v)
            if isinstance(parsed, list):
                return v
            else:
                return ""
        except json.JSONDecodeError:
            # 如果不是有效的JSON，尝试作为普通字符串处理
            return v


class CSVOCRValidationBatch(BaseModel):
    """CSV格式OCR校验批量数据模型"""
    records: List[CSVOCRValidationRecord] = Field(..., description="CSV格式OCR校验记录列表")
    batch_id: str = Field(default_factory=lambda: f"csv_batch_{datetime.now().strftime('%Y%m%d_%H%M%S')}", description="批次ID")
    source_file: Optional[str] = Field(default=None, description="源CSV文件路径")


class CSVDataProcessor:
    """CSV数据处理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def parse_csv_file(self, file_path: str) -> List[CSVOCRValidationRecord]:
        """
        解析CSV文件
        
        Args:
            file_path: CSV文件路径
            
        Returns:
            CSV格式的OCR校验记录列表
        """
        try:
            records = []
            
            # 使用pandas读取CSV文件
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 验证必需的列
            required_columns = ['bill_no', 'approval_result']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"CSV文件缺少必需的列: {missing_columns}")
            
            # 处理每一行数据
            for index, row in df.iterrows():
                try:
                    # 创建记录对象
                    record = CSVOCRValidationRecord(
                        bill_no=str(row['bill_no']),
                        approval_result=str(row['approval_result']),
                        complete_dtl=str(row.get('complete_dtl', '')),
                        accuracy_dtl=str(row.get('accuracy_dtl', '')),
                        consistency_dtl=str(row.get('consistency_dtl', ''))
                    )
                    records.append(record)
                    
                except Exception as e:
                    self.logger.warning(f"第{index + 1}行数据解析失败: {e}")
                    continue
            
            self.logger.info(f"成功解析CSV文件，共{len(records)}条记录")
            return records
            
        except Exception as e:
            self.logger.error(f"解析CSV文件失败: {e}")
            raise
    
    def parse_csv_string(self, csv_content: str) -> List[CSVOCRValidationRecord]:
        """
        解析CSV字符串内容
        
        Args:
            csv_content: CSV字符串内容
            
        Returns:
            CSV格式的OCR校验记录列表
        """
        try:
            records = []
            
            # 使用StringIO处理字符串
            from io import StringIO
            csv_file = StringIO(csv_content)
            
            # 使用csv模块读取
            reader = csv.DictReader(csv_file)
            
            for index, row in enumerate(reader):
                try:
                    # 创建记录对象
                    record = CSVOCRValidationRecord(
                        bill_no=str(row['bill_no']),
                        approval_result=str(row['approval_result']),
                        complete_dtl=str(row.get('complete_dtl', '')),
                        accuracy_dtl=str(row.get('accuracy_dtl', '')),
                        consistency_dtl=str(row.get('consistency_dtl', ''))
                    )
                    records.append(record)
                    
                except Exception as e:
                    self.logger.warning(f"第{index + 1}行数据解析失败: {e}")
                    continue
            
            self.logger.info(f"成功解析CSV字符串，共{len(records)}条记录")
            return records
            
        except Exception as e:
            self.logger.error(f"解析CSV字符串失败: {e}")
            raise
    
    def convert_to_standard_format(self, csv_records: List[CSVOCRValidationRecord]) -> List[Dict[str, Any]]:
        """
        将CSV格式转换为标准格式
        
        Args:
            csv_records: CSV格式的记录列表
            
        Returns:
            标准格式的记录列表
        """
        standard_records = []
        
        for csv_record in csv_records:
            # 转换借据号
            voucher_number = csv_record.bill_no
            
            # 转换审批结果
            review_result = csv_record.approval_result
            
            # 处理完备性详情
            completeness = self._format_detail_field(csv_record.complete_dtl, "完备性")
            
            # 处理准确性详情
            accuracy = self._format_detail_field(csv_record.accuracy_dtl, "准确性")
            
            # 处理一致性详情
            consistency = self._format_detail_field(csv_record.consistency_dtl, "一致性")
            
            # 创建标准格式记录
            standard_record = {
                "borrowing_voucher_number": voucher_number,
                "review_result": review_result,
                "completeness": completeness,
                "accuracy": accuracy,
                "consistency": consistency,
                "timestamp": datetime.now().isoformat()
            }
            
            standard_records.append(standard_record)
        
        self.logger.info(f"成功转换{len(standard_records)}条记录为标准格式")
        return standard_records
    
    def _format_detail_field(self, detail_field: str, field_type: str) -> str:
        """
        格式化详情字段
        
        Args:
            detail_field: 原始详情字段
            field_type: 字段类型（完备性/准确性/一致性）
            
        Returns:
            格式化后的字符串
        """
        if not detail_field or detail_field.strip() == "":
            return ""
        
        try:
            # 尝试解析JSON数组
            parsed = json.loads(detail_field)
            if isinstance(parsed, list) and len(parsed) > 0:
                # 将数组转换为描述性字符串
                items_str = "、".join(parsed)
                # return f"{field_type}检查不通过：缺少{items_str}"
                return f"{items_str}"
            else:
                return ""
        except json.JSONDecodeError:
            # 如果不是JSON格式，直接返回原内容
            if detail_field.strip():
                # return f"{field_type}检查不通过：{detail_field}"
                return f"{detail_field}"
            return ""
    
    def analyze_csv_data(self, csv_records: List[CSVOCRValidationRecord]) -> Dict[str, Any]:
        """
        分析CSV数据统计信息
        
        Args:
            csv_records: CSV格式的记录列表
            
        Returns:
            数据统计信息
        """
        if not csv_records:
            return {"error": "没有数据可分析"}
        
        # 基本统计
        total_records = len(csv_records)
        unique_vouchers = len(set(record.bill_no for record in csv_records))
        
        # 审批结果统计
        approval_stats = defaultdict(int)
        for record in csv_records:
            approval_stats[record.approval_result] += 1
        
        # 问题类型统计
        completeness_count = 0
        accuracy_count = 0
        consistency_count = 0
        
        for record in csv_records:
            if record.complete_dtl and record.complete_dtl.strip():
                completeness_count += 1
            if record.accuracy_dtl and record.accuracy_dtl.strip():
                accuracy_count += 1
            if record.consistency_dtl and record.consistency_dtl.strip():
                consistency_count += 1
        
        # 借据号格式分析
        scientific_notation_count = 0
        for record in csv_records:
            if 'E' in record.bill_no or 'e' in record.bill_no:
                scientific_notation_count += 1
        
        return {
            "total_records": total_records,
            "unique_vouchers": unique_vouchers,
            "approval_results": dict(approval_stats),
            "problem_types": {
                "completeness": completeness_count,
                "accuracy": accuracy_count,
                "consistency": consistency_count
            },
            "data_quality": {
                "scientific_notation_count": scientific_notation_count,
                "scientific_notation_percentage": round(scientific_notation_count / total_records * 100, 2)
            },
            "analysis_time": datetime.now().isoformat()
        }
    
    def validate_csv_data(self, csv_records: List[CSVOCRValidationRecord]) -> Dict[str, Any]:
        """
        验证CSV数据质量
        
        Args:
            csv_records: CSV格式的记录列表
            
        Returns:
            验证结果
        """
        validation_results = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "validated_records": 0
        }
        
        for index, record in enumerate(csv_records):
            try:
                # 验证借据号
                if not record.bill_no or record.bill_no.strip() == "":
                    validation_results["errors"].append(f"第{index + 1}行：借据号为空")
                    validation_results["is_valid"] = False
                
                # 验证审批结果
                if not record.approval_result or record.approval_result.strip() == "":
                    validation_results["errors"].append(f"第{index + 1}行：审批结果为空")
                    validation_results["is_valid"] = False
                
                # 验证详情字段格式
                for field_name, field_value in [
                    ("complete_dtl", record.complete_dtl),
                    ("accuracy_dtl", record.accuracy_dtl),
                    ("consistency_dtl", record.consistency_dtl)
                ]:
                    if field_value and field_value.strip():
                        try:
                            parsed = json.loads(field_value)
                            if not isinstance(parsed, list):
                                validation_results["warnings"].append(
                                    f"第{index + 1}行：{field_name}不是有效的JSON数组格式"
                                )
                        except json.JSONDecodeError:
                            validation_results["warnings"].append(
                                f"第{index + 1}行：{field_name}不是有效的JSON格式"
                            )
                
                validation_results["validated_records"] += 1
                
            except Exception as e:
                validation_results["errors"].append(f"第{index + 1}行：验证失败 - {e}")
                validation_results["is_valid"] = False
        
        validation_results["total_records"] = len(csv_records)
        validation_results["validation_time"] = datetime.now().isoformat()
        
        return validation_results

    def extract_problems_from_csv(self, csv_file_path: str) -> tuple:
        """
        从CSV文件中提取问题数据（按任务类型聚合）
        
        Args:
            csv_file_path: CSV文件路径
            
        Returns:
            tuple: (completeness_problems, accuracy_problems, df)
                - completeness_problems: 完备性问题列表 [(task_name, bill_nos, details), ...]
                - accuracy_problems: 准确性问题列表 [(file_type, bill_nos, details), ...]
                - df: 原始DataFrame
        """
        try:
            df = pd.read_csv(csv_file_path, encoding='utf-8')
            failed_bill_nos = df[df['approval_result'] == '不通过']['bill_no'].unique()
            df_filtered = df[df['bill_no'].isin(failed_bill_nos)]
            grouped = df_filtered.groupby('bill_no')
            
            # 完备性问题：统一到一个任务，使用字典避免重复借据号
            completeness_by_bill_no = {}
            
            # 准确性问题：按文件类型分组
            accuracy_by_file_type = defaultdict(lambda: {'bill_nos': [], 'details': []})
            
            for bill_no, group in grouped:
                bill_no_clean = self._clean_bill_no(bill_no)
                
                # 完备性问题 - 收集所有完备性问题的借据号和详情，同一借据号的详情合并
                comp_rows = group[group['complete'] == 'N']
                for _, row in comp_rows.iterrows():
                    detail = str(row.get('complete_dtl', '')).strip()
                    if detail:
                        # 格式化详情
                        formatted_detail = self._format_detail_field(detail, "完备性")
                        if formatted_detail:
                            if bill_no_clean not in completeness_by_bill_no:
                                completeness_by_bill_no[bill_no_clean] = []
                            completeness_by_bill_no[bill_no_clean].append(formatted_detail)
                
                # 准确性问题 - 按文件类型分组，避免同一借据号重复
                acc_rows = group[group['accuracy'] == 'N']
                for _, row in acc_rows.iterrows():
                    file_type = str(row.get('file_type', '')).strip()
                    accuracy_detail = str(row.get('accuracy_dtl', '')).strip()
                    if file_type:
                        # 检查借据号是否已存在，避免重复
                        if bill_no_clean not in accuracy_by_file_type[file_type]['bill_nos']:
                            accuracy_by_file_type[file_type]['bill_nos'].append(bill_no_clean)
                            accuracy_by_file_type[file_type]['details'].append(accuracy_detail if accuracy_detail else file_type)
                        else:
                            # 借据号已存在，合并详情
                            existing_index = accuracy_by_file_type[file_type]['bill_nos'].index(bill_no_clean)
                            existing_detail = accuracy_by_file_type[file_type]['details'][existing_index]
                            new_detail = accuracy_detail if accuracy_detail else file_type
                            if new_detail not in existing_detail:
                                accuracy_by_file_type[file_type]['details'][existing_index] = f"{existing_detail};{new_detail}"
                
                # 一致性问题 - 按文件类型归类到准确性问题
                cons_rows = group[group['consistency'] == 'N']
                for _, row in cons_rows.iterrows():
                    consistency_detail = str(row.get('consistency_dtl', '')).strip()
                    file_type = str(row.get('file_type', '')).strip() or "其他一致性问题"
                    if consistency_detail:
                        # 检查借据号是否已存在，避免重复
                        if bill_no_clean not in accuracy_by_file_type[file_type]['bill_nos']:
                            accuracy_by_file_type[file_type]['bill_nos'].append(bill_no_clean)
                            accuracy_by_file_type[file_type]['details'].append(consistency_detail)
                        else:
                            # 借据号已存在，合并详情
                            existing_index = accuracy_by_file_type[file_type]['bill_nos'].index(bill_no_clean)
                            existing_detail = accuracy_by_file_type[file_type]['details'][existing_index]
                            if consistency_detail not in existing_detail:
                                accuracy_by_file_type[file_type]['details'][existing_index] = f"{existing_detail};{consistency_detail}"
            
            # 组装完备性问题结果 - 去重借据号并合并详情
            completeness_problems = []
            if completeness_by_bill_no:
                bill_nos = list(completeness_by_bill_no.keys())
                details = []
                for bill_no in bill_nos:
                    # 合并同一借据号的所有详情，去重
                    bill_details = list(set(completeness_by_bill_no[bill_no]))
                    details.append(";".join(bill_details))
                completeness_problems.append(("统一完备性处理任务", bill_nos, details))
            
            # 组装准确性问题结果
            accuracy_problems = []
            for file_type, data in accuracy_by_file_type.items():
                if data['bill_nos']:
                    accuracy_problems.append((file_type, data['bill_nos'], data['details']))
            
            self.logger.info(f"成功提取问题数据：完备性任务 {len(completeness_problems)} 个，准确性任务 {len(accuracy_problems)} 个")
            
            return completeness_problems, accuracy_problems, df
            
        except Exception as e:
            self.logger.error(f"提取问题数据失败: {e}")
            raise

    def _clean_bill_no(self, bill_no):
        """清理借据号格式"""
        s = str(bill_no)
        if s.endswith('.0'):
            s = s[:-2]
        return s


# 测试函数
def test_csv_processor():
    """测试CSV处理器功能"""
    processor = CSVDataProcessor()
    
    # 测试数据
    test_csv_content = """bill_no,approval_result,complete_dtl,accuracy_dtl,consistency_dtl
202107300014,不通过,"["抵押合同","购车发票","租赁合同","车辆登记证"]",,"""
    
    try:
        # 解析CSV字符串
        csv_records = processor.parse_csv_string(test_csv_content)
        print(f"✅ 成功解析{len(csv_records)}条CSV记录")
        
        # 分析数据
        analysis = processor.analyze_csv_data(csv_records)
        print(f"📊 数据分析结果: {analysis}")
        
        # 验证数据
        validation = processor.validate_csv_data(csv_records)
        print(f"🔍 数据验证结果: {validation}")
        
        # 转换为标准格式
        standard_records = processor.convert_to_standard_format(csv_records)
        print(f"🔄 成功转换为{len(standard_records)}条标准格式记录")
        
        # 显示第一条转换后的记录
        if standard_records:
            print(f"📝 示例记录: {standard_records[0]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def clean_bill_no(bill_no):
    s = str(bill_no)
    if s.endswith('.0'):
        s = s[:-2]
    return s

def extract_problems_from_csv(csv_file_path):
    df = pd.read_csv(csv_file_path, encoding='utf-8')
    failed_bill_nos = df[df['approval_result'] == '不通过']['bill_no'].unique()
    df_filtered = df[df['bill_no'].isin(failed_bill_nos)]
    grouped = df_filtered.groupby('bill_no')
    completeness_problems = []  # [(bill_no, detail)]
    accuracy_problems = []      # [(bill_no, detail)]
    for bill_no, group in grouped:
        bill_no_clean = clean_bill_no(bill_no)
        # 完备性问题
        comp_rows = group[group['complete'] == 'N']
        for _, row in comp_rows.iterrows():
            detail = str(row.get('complete_dtl', '')).strip()
            if detail:
                completeness_problems.append((bill_no_clean, detail))
        # 准确性问题
        acc_rows = group[group['accuracy'] == 'N']
        for _, row in acc_rows.iterrows():
            detail = str(row.get('file_type', '') or row.get('accuracy_dtl', '')).strip()
            if detail:
                accuracy_problems.append((bill_no_clean, detail))
        cons_rows = group[group['consistency'] == 'N']
        for _, row in cons_rows.iterrows():
            detail = str(row.get('consistency_dtl', '')).strip()
            if detail:
                accuracy_problems.append((bill_no_clean, detail))
    # 去重
    completeness_problems = list({(b, d) for b, d in completeness_problems})
    accuracy_problems = list({(b, d) for b, d in accuracy_problems})
    return completeness_problems, accuracy_problems, df


if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    
    # 运行测试
    print("🧪 开始测试CSV处理器...")
    success = test_csv_processor()
    print(f"🏁 测试{'成功' if success else '失败'}") 