#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期字段的不同格式写入
"""

import json
import requests
import logging
import time
from datetime import datetime, timedelta
import calendar

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s"
)

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)
CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]

def get_access_token(corpid, corpsecret):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_document(access_token, title):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_name": title,
        "doc_type": 10  # 10=智能表格
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")

def add_sheet(access_token, docid, title, index=0):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    data = {
        "docid": docid,
        "title": title,
        "index": index
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result["properties"]["sheet_id"]
    else:
        raise Exception(f"添加子表失败: {result}")

def add_fields(access_token, docid, sheet_id, fields):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "fields": fields
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加字段失败: {result}")

def add_records(access_token, docid, sheet_id, records):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加记录失败: {result}")

def get_records(access_token, docid, sheet_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("records", [])
    else:
        raise Exception(f"获取记录失败: {result}")

def update_records(access_token, docid, sheet_id, records):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/update_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"更新记录失败: {result}")

def test_date_field():
    logging.info("🚀 开始测试日期字段功能...")
    access_token = get_access_token(CORPID, CORPSECRET)
    doc_title = f"日期字段测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, docurl = create_document(access_token, doc_title)
    logging.info(f"✅ 创建测试文档成功，docid: {docid}")
    sheet_id = add_sheet(access_token, docid, "日期测试表", 0)
    logging.info(f"✅ 添加子表成功，sheet_id: {sheet_id}")

    # 添加不同类型的日期字段
    fields = [
        {"field_title": "测试名称", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "日期时间字段", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}},
        {"field_title": "仅日期字段", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd", "auto_fill": False}},
        {"field_title": "自动填充创建时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}}
    ]
    add_fields(access_token, docid, sheet_id, fields)
    logging.info("✅ 添加日期字段成功")

    # 准备不同格式的日期数据
    now = datetime.now()
    tomorrow = now + timedelta(days=1)
    yesterday = now - timedelta(days=1)
    
    # 时间戳格式（秒级和毫秒级）
    now_timestamp_sec = int(now.timestamp())
    now_timestamp_ms = int(now.timestamp() * 1000)
    tomorrow_timestamp_sec = int(tomorrow.timestamp())
    yesterday_timestamp_ms = int(yesterday.timestamp() * 1000)
    
    # 测试记录 - 不同的日期格式
    records = [
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试1-时间戳秒级"}],
                "日期时间字段": [{"type": "datetime", "datetime": now_timestamp_sec}],
                "仅日期字段": [{"type": "datetime", "datetime": now_timestamp_sec}]
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试2-时间戳毫秒级"}],
                "日期时间字段": [{"type": "datetime", "datetime": now_timestamp_ms}],
                "仅日期字段": [{"type": "datetime", "datetime": now_timestamp_ms}]
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试3-直接数值秒级"}],
                "日期时间字段": tomorrow_timestamp_sec,
                "仅日期字段": tomorrow_timestamp_sec
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试4-直接数值毫秒级"}],
                "日期时间字段": yesterday_timestamp_ms,
                "仅日期字段": yesterday_timestamp_ms
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试5-字符串时间戳"}],
                "日期时间字段": [{"type": "datetime", "datetime": str(now_timestamp_sec)}],
                "仅日期字段": [{"type": "datetime", "datetime": str(now_timestamp_sec)}]
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试6-ISO格式字符串"}],
                "日期时间字段": [{"type": "text", "text": now.isoformat()}],
                "仅日期字段": [{"type": "text", "text": now.strftime("%Y-%m-%d")}]
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试7-特殊时间戳0"}],
                "日期时间字段": [{"type": "datetime", "datetime": 0}],
                "仅日期字段": [{"type": "datetime", "datetime": 0}]
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试8-未来时间"}],
                "日期时间字段": [{"type": "datetime", "datetime": int((now + timedelta(days=30)).timestamp())}],
                "仅日期字段": [{"type": "datetime", "datetime": int((now + timedelta(days=30)).timestamp())}]
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试9-过去时间"}],
                "日期时间字段": [{"type": "datetime", "datetime": int((now - timedelta(days=365)).timestamp())}],
                "仅日期字段": [{"type": "datetime", "datetime": int((now - timedelta(days=365)).timestamp())}]
            }
        },
        {
            "values": {
                "测试名称": [{"type": "text", "text": "测试10-空值测试"}],
                "日期时间字段": [],
                "仅日期字段": []
            }
        }
    ]
    
    logging.info("📤 开始添加测试记录...")
    add_result = add_records(access_token, docid, sheet_id, records)
    logging.info(f"✅ 添加记录结果: {add_result}")

    # 查询并分析结果
    time.sleep(2)
    logging.info("📊 查询并分析结果...")
    recs = get_records(access_token, docid, sheet_id)
    
    for i, rec in enumerate(recs):
        values = rec.get("values", {})
        test_name = ""
        datetime_value = "未设置"
        date_value = "未设置"
        auto_fill_value = "未设置"
        
        # 获取测试名称
        if "测试名称" in values and values["测试名称"]:
            test_name = values["测试名称"][0].get("text", "")
        
        # 获取日期时间字段值
        if "日期时间字段" in values:
            datetime_value = values["日期时间字段"]
        
        # 获取仅日期字段值
        if "仅日期字段" in values:
            date_value = values["仅日期字段"]
            
        # 获取自动填充字段值
        if "自动填充创建时间" in values:
            auto_fill_value = values["自动填充创建时间"]
        
        logging.info(f"记录{i+1}: {test_name}")
        logging.info(f"  日期时间字段: {datetime_value} (类型: {type(datetime_value)})")
        logging.info(f"  仅日期字段: {date_value} (类型: {type(date_value)})")
        logging.info(f"  自动填充字段: {auto_fill_value} (类型: {type(auto_fill_value)})")
        logging.info("---")

    # 测试日期字段更新
    if recs and len(recs) >= 3:
        logging.info("🔄 开始测试日期字段更新...")

        # 准备更新用的时间戳
        update_time = datetime.now() + timedelta(hours=2)
        update_timestamp_sec = int(update_time.timestamp())
        update_timestamp_ms = int(update_time.timestamp() * 1000)

        # 测试不同的更新格式
        update_tests = [
            {
                "name": "更新格式1-标准datetime格式",
                "record_index": 0,
                "values": {
                    "日期时间字段": [{"type": "datetime", "datetime": update_timestamp_sec}]
                }
            },
            {
                "name": "更新格式2-直接数值",
                "record_index": 1,
                "values": {
                    "日期时间字段": update_timestamp_ms
                }
            },
            {
                "name": "更新格式3-字符串时间戳",
                "record_index": 2,
                "values": {
                    "日期时间字段": [{"type": "datetime", "datetime": str(update_timestamp_sec)}]
                }
            }
        ]

        for test in update_tests:
            try:
                record_id = recs[test["record_index"]]["record_id"]
                update_data = {
                    "record_id": record_id,
                    "values": test["values"]
                }

                update_result = update_records(access_token, docid, sheet_id, [update_data])
                logging.info(f"✅ {test['name']} 更新成功: {update_result}")

            except Exception as e:
                logging.info(f"❌ {test['name']} 更新失败: {e}")

        # 查看更新后的结果
        time.sleep(2)
        logging.info("📊 查看更新后的结果...")
        updated_recs = get_records(access_token, docid, sheet_id)

        for i, rec in enumerate(updated_recs[:3]):
            values = rec.get("values", {})
            test_name = ""
            datetime_value = "未设置"

            if "测试名称" in values and values["测试名称"]:
                test_name = values["测试名称"][0].get("text", "")

            if "日期时间字段" in values:
                datetime_value = values["日期时间字段"]

            logging.info(f"更新后记录{i+1}: {test_name} -> 日期时间: {datetime_value}")

    print(f"\n🎯 测试完成！")
    print(f"📋 测试文档链接: {docurl}")
    print(f"💡 请在企业微信中查看不同日期格式的显示效果")
    print(f"🔍 重点观察:")
    print(f"   1. 不同时间戳格式的显示效果")
    print(f"   2. 日期时间字段 vs 仅日期字段的区别")
    print(f"   3. 自动填充字段是否正确填充创建时间")
    print(f"   4. 更新操作是否成功")

    return docurl

if __name__ == "__main__":
    print("📅 日期字段格式测试工具")
    print("=" * 40)
    
    try:
        doc_url = test_date_field()
        if doc_url:
            print(f"\n✅ 测试成功完成")
            print(f"📋 文档链接: {doc_url}")
        else:
            print(f"\n❌ 测试失败")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
