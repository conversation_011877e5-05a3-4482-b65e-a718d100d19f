# testprogress
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
import logging
import time
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s"
)

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)
CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]

def get_access_token(corpid, corpsecret):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_document(access_token, title):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_name": title,
        "doc_type": 10  # 10=智能表格
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")

def add_sheet(access_token, docid, title, index=0):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    data = {
        "docid": docid,
        "title": title,
        "index": index
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result["properties"]["sheet_id"]
    else:
        raise Exception(f"添加子表失败: {result}")

def add_fields(access_token, docid, sheet_id, fields):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "fields": fields
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加字段失败: {result}")

def add_records(access_token, docid, sheet_id, records):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加记录失败: {result}")

def get_records(access_token, docid, sheet_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("records", [])
    else:
        raise Exception(f"获取记录失败: {result}")

def update_records(access_token, docid, sheet_id, records):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/update_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"更新记录失败: {result}")

def test_progress_field():
    logging.info("🚀 开始测试进度字段功能...")
    access_token = get_access_token(CORPID, CORPSECRET)
    doc_title = f"进度字段测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, docurl = create_document(access_token, doc_title)
    logging.info(f"✅ 创建测试文档成功，docid: {docid}")
    sheet_id = add_sheet(access_token, docid, "进度测试表", 0)
    logging.info(f"✅ 添加子表成功，sheet_id: {sheet_id}")

    fields = [
        {"field_title": "任务名称", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", "property_single_select": {
            "options": [
                {"text": "未开始", "style": 18},
                {"text": "进行中", "style": 12},
                {"text": "已完成", "style": 16}
            ]
        }},
        {"field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", "property_progress": {"decimal_places": 0}}
    ]
    add_fields(access_token, docid, sheet_id, fields)
    logging.info("✅ 添加字段成功")

    # 测试添加记录 - 测试各种数值和格式
    records = [
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务1-错误格式"}],
                "任务状态": [{"type": "single_select", "text": "未开始"}],
                "进度": [{"type": "number", "number": 0}]  # 错误格式
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务2-正确格式0"}],
                "任务状态": [{"type": "single_select", "text": "未开始"}],
                "进度": 0  # 正确格式 - 0%
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务3-正确格式25"}],
                "任务状态": [{"type": "single_select", "text": "进行中"}],
                "进度": 25  # 正确格式 - 25%
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务4-正确格式50"}],
                "任务状态": [{"type": "single_select", "text": "进行中"}],
                "进度": 50  # 正确格式 - 50%
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务5-正确格式75"}],
                "任务状态": [{"type": "single_select", "text": "进行中"}],
                "进度": 75  # 正确格式 - 75%
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务6-正确格式100"}],
                "任务状态": [{"type": "single_select", "text": "已完成"}],
                "进度": 100  # 正确格式 - 100%
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务7-浮点数25.5"}],
                "任务状态": [{"type": "single_select", "text": "进行中"}],
                "进度": 25.5  # 浮点数
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务8-浮点数66.7"}],
                "任务状态": [{"type": "single_select", "text": "进行中"}],
                "进度": 66.7  # 浮点数
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务9-超出范围150"}],
                "任务状态": [{"type": "single_select", "text": "已完成"}],
                "进度": 150  # 超出100%的值
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务10-负数-10"}],
                "任务状态": [{"type": "single_select", "text": "未开始"}],
                "进度": -10  # 负数
            }
        }
    ]
    add_records(access_token, docid, sheet_id, records)
    logging.info("✅ 添加测试记录成功")

    # 查询并打印
    time.sleep(1)
    recs = get_records(access_token, docid, sheet_id)
    for i, rec in enumerate(recs):
        logging.info(f"记录{i+1}: {rec}")

    # 测试更新 - 测试不同的更新格式和数值
    if recs and len(recs) >= 3:
        logging.info("🔄 开始测试进度字段更新...")

        # 测试1: 错误格式更新
        record_id_1 = recs[0]["record_id"]
        update_1 = {
            "record_id": record_id_1,
            "values": {
                "进度": [{"type": "number", "number": 85}]  # 错误格式
            }
        }
        try:
            update_records(access_token, docid, sheet_id, [update_1])
            logging.info("✅ 错误格式更新成功（意外）")
        except Exception as e:
            logging.info(f"❌ 错误格式更新失败（预期）: {e}")

        # 测试2: 正确格式更新
        record_id_2 = recs[1]["record_id"]
        update_2 = {
            "record_id": record_id_2,
            "values": {
                "进度": 85  # 正确格式
            }
        }
        try:
            update_records(access_token, docid, sheet_id, [update_2])
            logging.info("✅ 正确格式更新成功")
        except Exception as e:
            logging.info(f"❌ 正确格式更新失败: {e}")

        # 测试3: 浮点数更新
        record_id_3 = recs[2]["record_id"]
        update_3 = {
            "record_id": record_id_3,
            "values": {
                "进度": 33.3  # 浮点数
            }
        }
        try:
            update_records(access_token, docid, sheet_id, [update_3])
            logging.info("✅ 浮点数更新成功")
        except Exception as e:
            logging.info(f"❌ 浮点数更新失败: {e}")

        # 等待更新完成后查看结果
        time.sleep(2)
        logging.info("📊 查看更新后的所有记录:")
        recs = get_records(access_token, docid, sheet_id)
        for i, rec in enumerate(recs):
            task_name = ""
            progress_value = "未知"
            values = rec.get("values", {})

            # 获取任务名称
            if "任务名称" in values and values["任务名称"]:
                task_name = values["任务名称"][0].get("text", "")

            # 获取进度值
            if "进度" in values:
                progress_value = values["进度"]

            logging.info(f"记录{i+1}: {task_name} -> 进度: {progress_value} (类型: {type(progress_value)})")

    # 额外测试：批量更新不同数值
    logging.info("🔄 测试批量更新不同进度数值...")
    if len(recs) >= 5:
        batch_updates = []
        test_values = [10, 30, 60, 90, 99.9]

        for i, test_value in enumerate(test_values):
            if i < len(recs):
                batch_updates.append({
                    "record_id": recs[i]["record_id"],
                    "values": {
                        "进度": test_value
                    }
                })

        try:
            update_records(access_token, docid, sheet_id, batch_updates)
            logging.info("✅ 批量更新成功")

            # 查看批量更新结果
            time.sleep(2)
            final_recs = get_records(access_token, docid, sheet_id)
            logging.info("📊 批量更新后的进度值:")
            for i, rec in enumerate(final_recs[:5]):
                values = rec.get("values", {})
                progress_value = values.get("进度", "未知")
                task_name = ""
                if "任务名称" in values and values["任务名称"]:
                    task_name = values["任务名称"][0].get("text", "")
                logging.info(f"  {task_name}: {progress_value}")

        except Exception as e:
            logging.info(f"❌ 批量更新失败: {e}")

    print(f"测试文档链接: {docurl}")

if __name__ == "__main__":
    test_progress_field()