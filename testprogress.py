# testprogress
#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import requests
import logging
import time
from datetime import datetime

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s"
)

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)
CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]

def get_access_token(corpid, corpsecret):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_document(access_token, title):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_name": title,
        "doc_type": 10  # 10=智能表格
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")

def add_sheet(access_token, docid, title, index=0):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    data = {
        "docid": docid,
        "title": title,
        "index": index
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result["properties"]["sheet_id"]
    else:
        raise Exception(f"添加子表失败: {result}")

def add_fields(access_token, docid, sheet_id, fields):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "fields": fields
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加字段失败: {result}")

def add_records(access_token, docid, sheet_id, records):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"添加记录失败: {result}")

def get_records(access_token, docid, sheet_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/get_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("records", [])
    else:
        raise Exception(f"获取记录失败: {result}")

def update_records(access_token, docid, sheet_id, records):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/update_records?access_token={access_token}"
    data = {
        "docid": docid,
        "sheet_id": sheet_id,
        "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
        "records": records
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result
    else:
        raise Exception(f"更新记录失败: {result}")

def test_progress_field():
    logging.info("🚀 开始测试进度字段功能...")
    access_token = get_access_token(CORPID, CORPSECRET)
    doc_title = f"进度字段测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, docurl = create_document(access_token, doc_title)
    logging.info(f"✅ 创建测试文档成功，docid: {docid}")
    sheet_id = add_sheet(access_token, docid, "进度测试表", 0)
    logging.info(f"✅ 添加子表成功，sheet_id: {sheet_id}")

    fields = [
        {"field_title": "任务名称", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", "property_single_select": {
            "options": [
                {"text": "未开始", "style": 18},
                {"text": "进行中", "style": 12},
                {"text": "已完成", "style": 16}
            ]
        }},
        {"field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", "property_progress": {"decimal_places": 0}}
    ]
    add_fields(access_token, docid, sheet_id, fields)
    logging.info("✅ 添加字段成功")

    # 测试添加记录
    records = [
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务1"}],
                "任务状态": [{"type": "single_select", "text": "未开始"}],
                "进度": [{"type": "number", "number": 0}]
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务2"}],
                "任务状态": [{"type": "single_select", "text": "进行中"}],
                "进度": 50
            }
        },
        {
            "values": {
                "任务名称": [{"type": "text", "text": "测试任务3"}],
                "任务状态": [{"type": "single_select", "text": "已完成"}],
                "进度": [{"type": "double", "double": 100}]
            }
        }
    ]
    add_records(access_token, docid, sheet_id, records)
    logging.info("✅ 添加测试记录成功")

    # 查询并打印
    time.sleep(1)
    recs = get_records(access_token, docid, sheet_id)
    for i, rec in enumerate(recs):
        logging.info(f"记录{i+1}: {rec}")

    # 测试更新
    if recs:
        record_id = recs[0]["record_id"]
        update = {
            "record_id": record_id,
            "values": {
                "进度": [{"type": "number", "number": 75}]
            }
        }
        update_records(access_token, docid, sheet_id, [update])
        logging.info("✅ 更新进度字段成功")
        time.sleep(1)
        recs = get_records(access_token, docid, sheet_id)
        for i, rec in enumerate(recs):
            logging.info(f"更新后记录{i+1}: {rec}")

    print(f"测试文档链接: {docurl}")

if __name__ == "__main__":
    test_progress_field()