好的，我会完整梳理你提供的项目代码，输出详细的功能介绍和使用说明，适合其他开发者快速理解和上手。内容将包括项目结构、主要功能、核心流程、接口说明、配置说明、常见问题等，最终以 Markdown 格式输出。

---

# 企业微信OCR校验数据自动化处理系统

## 目录

- [项目简介](#项目简介)
- [项目结构](#项目结构)
- [主要功能](#主要功能)
- [核心流程说明](#核心流程说明)
- [API接口说明](#api接口说明)
- [配置文件说明](#配置文件说明)
- [常见问题与注意事项](#常见问题与注意事项)
- [开发与运行环境](#开发与运行环境)
- [致开发者](#致开发者)

---

## 项目简介

本项目旨在实现企业微信环境下，针对OCR校验数据的自动化处理、问题归类、任务分配、文档生成与推送、以及后续的自动监控和字段修正。通过自动化流程，极大提升了数据处理效率和协作透明度，适用于需要批量处理OCR校验结果、自动分派任务、并在企业微信中进行协作的场景。

---

## 项目结构

```
wxwork/
├── cloud_service/                # 云服务相关代码与文档
├── data/                         # 存放各类CSV数据文件
├── logs/                         # 日志输出目录
├── ocr_upload/                   # 处理后最新的csv文件
├── test_wxwork/                  # 各类测试脚本
├── csv_ocr_validation_handler.py # CSV数据解析与校验核心模块
├── csv_uploader_with_monitor.py  # 主流程与监控逻辑
├── wxwork_smartsheet_api_client.py # 企业微信Smartsheet API封装
├── ocr_csv_api.py                # FastAPI接口服务
├── config.json                   # 配置文件
├── requirements.txt              # 依赖包列表
├── 功能说明.md                   # 功能说明文档
└── ...                           # 其他文件
```

---

## 主要功能

### 1. CSV OCR校验数据解析与标准化

- 支持解析本地CSV文件或字符串，自动识别借据号、审批结果、完备性/准确性/一致性详情等字段。
- 自动处理科学计数法格式的借据号，保证数据准确。
- 校验数据完整性与格式，输出详细的错误与警告信息。
- 支持将原始数据转换为标准格式，便于后续处理。

### 2. 问题归类与任务聚合

- 自动分析CSV中“不通过”记录，按完备性、准确性、一致性问题进行聚合。
- 完备性问题统一为一个任务，准确性/一致性问题按文件类型分组。
- 自动去重、合并详情，便于任务分派。

### 3. 企业微信文档自动生成与推送

- 自动创建企业微信智能表格文档，包含原始数据、问题汇总、完备性问题、准确性问题四个子表。
- 每个子表字段自动配置，支持多种类型（文本、单选、多选、用户、日期等）。
- 自动推送文档链接到企业微信（支持模板卡片和文本消息），并设置文档权限。

### 4. 任务分配与字段自动修正

- 根据配置自动分配开发/产品/OCR人员，任务原因与任务名称自动映射。
- 支持任务状态、处理人员、OCR人员等字段的自动修正，保证流程闭环。

### 5. 实时监控与自动处理

- 实时监控完备性/准确性子表的“重新OCR状态”，自动检测状态变更并处理最新CSV。
- 支持文件变更自动触发处理，保证数据与任务同步。
- 支持多线程并发监控，互不影响。

### 6. API接口服务

- 提供基于FastAPI的文件上传接口，支持初始CSV、完备性CSV、准确性CSV的上传与自动处理。
- 支持异步处理与自动触发主流程。

---

## 核心流程说明

### 1. 数据上传与处理

1. 用户通过API或手动方式上传初始CSV文件。
2. 系统自动解析CSV，校验数据，归类问题，生成标准格式。
3. 自动创建企业微信智能表格文档，生成四个子表（原始数据、问题汇总、完备性问题、准确性问题）。
4. 自动推送文档链接到企业微信，相关人员可直接点击处理。

### 2. 任务分配与字段修正

- 根据配置文件的`reason_task_person_map`，自动将不同任务原因映射到对应任务名称和处理人员。
- 任务状态变更时，自动补全OCR人员、处理人员等字段，减少人工干预。

### 3. 实时监控与自动处理

- 系统持续监控完备性/准确性子表的“重新OCR状态”字段。
- 检测到状态变为“已完成”或CSV文件有更新时，自动处理最新数据并同步到企业微信文档。
- 支持多线程并发监控，保证高效响应。

---

## API接口说明

### 1. 上传初始CSV

- **接口**：`POST /upload_initial_csv`
- **参数**：`file`（上传的CSV文件）
- **说明**：用于首次上传和初始化流程，始终允许上传，上传后自动触发主流程，生成文档并推送。

### 2. 补传CSV（完备性/准确性统一入口）

- **接口**：`POST /upload_patch_csv`
- **参数**：`file`（上传的CSV文件）
- **说明**：只有当“完备性子表”和“准确性子表”所有“重新OCR状态”都为“已完成”时，才允许上传补传csv。上传后只保存文件（路径为`ocr_upload/patch_latest.csv`），不自动处理，需等待监控线程或后续流程。

### 3. 已移除接口

- `/upload_completeness_csv` 和 `/upload_accuracy_csv` 已废弃，统一由 `/upload_patch_csv` 替代。

---

## 配置文件说明（config.json）

```json
{
  "corpid": "企业微信CorpID",
  "corpsecret": "企业微信Secret",
  "csv_files": ["ocr_upload/initial_latest.csv"],
  "monitor_interval": 5,
  "deadline_minutes": 10,
  "csv_upload": {
    "initial_csv": "ocr_upload/initial_latest.csv",
    "completeness_csv": "ocr_upload/completeness_latest.csv",
    "accuracy_csv": "ocr_upload/accuracy_latest.csv"
  },
  "users": {
    "开发人员": [{"user_id": "ZhengKaiXin", "name": "郑凯鑫"}],
    "产品人员": [{"user_id": "pluviophile", "name": "刘青昀"}],
    "ocr人员": [{"user_id": "ZhengKaiXin", "name": "ZhengKaiXin"}]
  },
  "reason_task_person_map": {
    "影像件模糊": {"task": "调整ocr通过阈值", "person": "开发人员"},
    "内容错误": {"task": "与合作机构沟通核对并补传", "person": "产品人员"},
    "系统问题": {"task": "修复系统问题", "person": "开发人员"}
  },
  "doc_permission": {
    "enable_corp_external": true,
    "corp_external_auth": 2,
    "enable_corp_internal": true,
    "corp_internal_auth": 2
  }
}
```

- **corpid/corpsecret**：企业微信API凭证
- **csv_upload**：各类CSV文件的保存路径
- **users**：各类角色的企业微信用户ID
- **reason_task_person_map**：任务原因与任务名称/人员的映射
- **doc_permission**：文档权限设置

---

## 常见问题与注意事项

1. **依赖安装**  
   请确保已安装`requirements.txt`中的所有依赖（如`pandas`, `requests`, `fastapi`, `uvicorn`, `pydantic`等）。

2. **企业微信API权限**  
   需保证企业微信应用有文档、消息等相关接口权限，且用户ID正确。

3. **CSV格式要求**  
   - 必须包含`bill_no`、`approval_result`等核心字段。
   - 详情字段建议为JSON数组格式，便于自动解析。

4. **日志查看**  
   日志文件位于`logs/app.log`，便于排查问题。

5. **多线程监控**  
   监控功能为多线程，若需关闭请手动终止进程。

6. **API服务端口**  
   FastAPI默认监听8002端口，如需更改请修改`ocr_csv_api.py`。

---

## 开发与运行环境

- **Python版本**：建议3.7及以上
- **依赖管理**：`pip install -r requirements.txt`
- **直接启动API服务（建议），在http://localhost:8002/docs上传对应文件**：  
  ```bash
  python ocr_csv_api.py
  ```
- **日志查看**：  
  查看`logs/app.log`

---

## 致开发者

本项目已高度自动化，适合批量处理OCR校验数据、自动分派任务、并在企业微信中协作。  
如需二次开发，建议从以下文件入手：

- **csv_ocr_validation_handler.py**：数据解析与校验核心
- **csv_uploader_with_monitor.py**：主流程与监控逻辑
- **wxwork_smartsheet_api_client.py**：企业微信API封装
- **ocr_csv_api.py**：API接口服务

如有问题，欢迎查阅`功能说明.md`或联系项目维护者。

---

**（本说明文档可直接提供给新开发者或运维人员使用）**