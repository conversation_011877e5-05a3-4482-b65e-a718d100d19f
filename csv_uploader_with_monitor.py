#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV上传+监控企业微信字表字段自动修正
"""

import time
import logging
import json
import os
import glob
import requests
from datetime import datetime, timedelta
from csv_ocr_validation_handler import CSVDataProcessor
from wxwork_smartsheet_api_client import WxworkSmartsheetApiClient
import threading

def calculate_progress_value(values):
    """
    根据多个字段状态计算进度值
    阶段1：指定开发人员 + 选择任务原因 → 33
    阶段2：任务状态改为已完成 → 66  
    阶段3：重新OCR状态改为已完成 → 100%
    优化：进度只会递增或保持，不会回退为0
    """
    task_status = next((v.get("text", "") for v in values.get("任务状态", []) if v.get("text")), "")
    reocr_status = next((v.get("text", "") for v in values.get("重新OCR状态", []) if v.get("text")), "")
    has_ocr_persons = bool(values.get("OCR人员", []))

    # 进度递增逻辑
    if reocr_status == "已完成":
        return 100
    elif task_status == "已完成" and has_ocr_persons:
        return 66
    elif task_status in ["进行中", "未开始"]:
        return 33
    else:
        # 如果状态异常，返回当前进度，不回退为0
        current_progress = values.get("进度", 0)
        try:
            return int(current_progress)
        except Exception:
            return 0

def has_status_changed(old_values, new_values):
    """
    检测状态是否发生变化
    """
    key_fields = ["任务状态", "重新OCR状态", "OCR人员", "任务原因"]
    for field in key_fields:
        old_val = old_values.get(field, [])
        new_val = new_values.get(field, [])
        if old_val != new_val:
            return True
    return False

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]
CSV_FILES = [config["csv_upload"]["initial_csv"]]
MONITOR_INTERVAL = config.get("monitor_interval", 300)
DEADLINE_MINUTES = config.get("deadline_minutes", 10)  # 截止时间间隔（分钟）
REASON_TASK_PERSON_MAP = config["reason_task_person_map"]
USERS = config.get("users", {})
COMPLETENESS_CSV = config.get("csv_upload", {}).get("completeness_csv", "ocr_upload/completeness_latest.csv")
ACCURACY_CSV = config.get("csv_upload", {}).get("accuracy_csv", "ocr_upload/accuracy_latest.csv")
# 新增：文档权限相关配置
DOC_PERMISSION = config.get("doc_permission", {
    "enable_corp_external": True,
    "corp_external_auth": 1
})

# 日志配置：输出到 logs/app.log 和控制台
log_dir = "logs"
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, "app.log")
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s %(levelname)s %(name)s: %(message)s",
    handlers=[
        logging.FileHandler(log_file, encoding="utf-8"),
        logging.StreamHandler()
    ]
)

class CsvUploadStateManager:
    """全局CSV上传与监控状态管理器（线程安全）"""
    def __init__(self):
        self.lock = threading.Lock()
        self.completeness_all_done = False
        self.accuracy_all_done = False
        self.allow_csv_upload = False
        self.last_handled_round = 0  # 用于防止重复处理
        self.round_counter = 0       # 每轮全部完成+上传后自增
        self.notified_this_round = False  # 本轮是否已推送提醒
        self.last_uploaded_csv = None
        self.last_uploaded_time = None

    def set_completeness_all_done(self, value: bool):
        with self.lock:
            self.completeness_all_done = value
            self._update_allow_csv_upload()

    def set_accuracy_all_done(self, value: bool):
        with self.lock:
            self.accuracy_all_done = value
            self._update_allow_csv_upload()

    def _update_allow_csv_upload(self):
        prev = self.allow_csv_upload
        self.allow_csv_upload = self.completeness_all_done and self.accuracy_all_done
        if self.allow_csv_upload and not prev:
            self.round_counter += 1
            self.notified_this_round = False  # 新一轮可推送

    def can_upload_csv(self):
        with self.lock:
            return self.allow_csv_upload, self.round_counter

    def mark_csv_handled(self):
        with self.lock:
            self.allow_csv_upload = False
            self.last_handled_round = self.round_counter
            self.notified_this_round = False  # 处理后重置

    def is_handled_this_round(self):
        with self.lock:
            return self.last_handled_round == self.round_counter

    def mark_notified(self):
        with self.lock:
            self.notified_this_round = True

    def notified(self):
        with self.lock:
            return self.notified_this_round

    def set_last_uploaded(self, csv_path, upload_time):
        with self.lock:
            self.last_uploaded_csv = csv_path
            self.last_uploaded_time = upload_time

# 全局状态管理器实例
csv_upload_state_manager = CsvUploadStateManager()

def get_latest_csv_from_folder(folder_path):
    """获取指定文件夹下最新的csv文件路径"""
    csv_files = glob.glob(os.path.join(folder_path, '*.csv'))
    if not csv_files:
        return None
    latest_file = max(csv_files, key=os.path.getmtime)
    return latest_file

def monitor_accuracy_sheet(access_token, docid, accuracy_sheet_id, interval=MONITOR_INTERVAL):
    """
    监控准确性问题子表的重新OCR状态，只有全部为已完成时才允许上传csv。
    """
    client = WxworkSmartsheetApiClient(access_token)
    logging.info(f"开始监控准确性问题子表（sheet_id={accuracy_sheet_id}），每{interval}秒检查一次...")
    ocr_users = USERS.get("ocr人员", [])
    ocr_user_objs = [{"type": "user", "user_id": u["user_id"]} for u in ocr_users]
    while True:
        try:
            records = client.get_records(docid=docid, sheet_id=accuracy_sheet_id)
            total = len(records)
            done = 0
            for record in records:
                values = record.get("values", {})
                reocr_status = ""
                if "重新OCR状态" in values:
                    reocr_status = next((v.get("text", "") for v in values["重新OCR状态"] if v.get("text")), "")
                if reocr_status == "已完成":
                    done += 1
            all_done = (done == total and total > 0)
            logging.info(f"[准确性子表] 重新OCR已完成 {done}/{total}")
            csv_upload_state_manager.set_accuracy_all_done(all_done)
            if all_done:
                logging.info("[准确性子表] 所有记录已完成！")
            # 字段自动修正逻辑增强
            for record in records:
                values = record.get("values", {})
                record_id = record.get("record_id")
                reasons = [v.get("text", "") for v in values.get("任务原因", [])]
                update_values = {}
                # 选择任务原因后，自动联动任务名称和处理人员，并将任务状态设为进行中
                if reasons:
                    reason = reasons[0]
                    mapping = REASON_TASK_PERSON_MAP.get(reason)
                    if mapping:
                        target_task = mapping["task"]
                        target_person = mapping["person"]
                        user_list = USERS.get(target_person, [])
                        wx_users = [{"type": "user", "user_id": u["user_id"]} for u in user_list]
                        task_names = [v.get("text", "") for v in values.get("任务名称", [])]
                        persons = [v.get("user_id", v.get("text", "")) for v in values.get("处理人员", [])]
                        need_update = (target_task not in task_names) or (not wx_users or any(u["user_id"] not in persons for u in user_list))
                        if need_update:
                            logging.info(f"发现需修正的记录: record_id={record_id}, 任务原因={reason}, 原任务名称={task_names}, 原处理人员={persons}")
                            update_values["任务名称"] = [{"type": "select", "text": target_task}]
                            update_values["处理人员"] = wx_users
                            # 只有任务状态为"未开始"时，才自动设为进行中
                            task_status = [v.get("text", "") for v in values.get("任务状态", [])]
                            if "未开始" in task_status:
                                update_values["任务状态"] = [{"type": "single_select", "text": "进行中"}]
                            # 字段联动时同步更新进度
                            progress_value = calculate_progress_value(values)
                            current_progress = values.get("进度", 0)
                            try:
                                current_progress = int(current_progress)
                            except Exception:
                                current_progress = 0
                            if progress_value > current_progress:
                                update_values["进度"] = progress_value
                # 任务状态为已完成时，自动补全OCR人员，并将重新OCR状态设为进行中
                task_status = [v.get("text", "") for v in values.get("任务状态", [])]
                reocr_status = [v.get("text", "") for v in values.get("重新OCR状态", [])]
                ocr_persons = [v.get("user_id", v.get("text", "")) for v in values.get("OCR人员", [])]
                
                if "已完成" in task_status:                   # 自动指定OCR人员（如果未指定）
                    if not ocr_user_objs or any(u["user_id"] not in ocr_persons for u in ocr_users):
                        logging.info(f"任务已完成，自动指定OCR人员: record_id={record_id}, 原OCR人员={ocr_persons}")
                        update_values["OCR人员"] = ocr_user_objs
                    
                    # 只有在重新OCR状态不是已完成且不是"进行中"时才设置为"进行中"
                    if "已完成" not in reocr_status and "进行中" not in reocr_status:
                        logging.info(f"任务已完成，设置重新OCR状态为进行中: record_id={record_id}")
                        update_values["重新OCR状态"] = [{"type": "single_select", "text": "进行中"}]
                
                # 使用新的进度计算逻辑
                progress_value = calculate_progress_value(values)
                current_progress = values.get("进度", 0)
                try:
                    current_progress = int(current_progress)
                except Exception:
                    current_progress = 0
                # 只允许进度递增或保持，不回退
                if progress_value > current_progress:
                    logging.info(f"进度更新: record_id={record_id}, 当前进度={current_progress}%, 新进度={progress_value}%")
                    update_values["进度"] = progress_value
                if update_values:
                    client.update_records(docid, accuracy_sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", [
                        {
                            "record_id": record_id,
                            "values": update_values
                        }
                    ])
                    logging.info(f"已自动修正record_id={record_id}的字段: {list(update_values.keys())}")
            # 如果两个子表都完成，log提示可以上传新文件
            if csv_upload_state_manager.allow_csv_upload:
                logging.info("[系统] 两个子表都已完成，可以上传新文件！")
                # 推送提醒（只推送一次）
                if not csv_upload_state_manager.notified():
                    batch_users = USERS.get("batch_users", [])
                    touser = "|".join([u["user_id"] for u in batch_users])
                    agentid = 1000002
                    # 丰富推送内容
                    csv_name = csv_upload_state_manager.last_uploaded_csv or "-"
                    upload_time = csv_upload_state_manager.last_uploaded_time or "-"
                    msg = f"所有OCR任务已完成，可上传跑批后的csv文件。\n原始上传文件: {csv_name}\n上传时间: {upload_time}"
                    send_wecom_text_message(access_token, agentid, touser, msg)
                    send_wecom_template_card(access_token, agentid, touser, "", f"可上传新csv（{csv_name}）", f"notify_{docid}_{csv_upload_state_manager.round_counter}")
                    logging.info(f"[系统] 已推送可上传新文件提醒给: {touser}")
                    csv_upload_state_manager.mark_notified()
        except Exception as e:
            logging.error(f"准确性监控异常: {e}")
        time.sleep(interval)


def monitor_completeness_sheet(access_token, docid, completeness_sheet_id, interval=MONITOR_INTERVAL):
    """
    监控完备性问题子表的重新OCR状态，只有全部为已完成时才允许上传csv。
    """
    client = WxworkSmartsheetApiClient(access_token)
    logging.info(f"开始监控完备性问题子表（sheet_id={completeness_sheet_id}），每{interval}秒检查一次...")
    ocr_users = USERS.get("ocr人员", [])
    ocr_user_objs = [{"type": "user", "user_id": u["user_id"]} for u in ocr_users]
    while True:
        try:
            records = client.get_records(docid=docid, sheet_id=completeness_sheet_id)
            total = len(records)
            done = 0
            for record in records:
                values = record.get("values", {})
                reocr_status = ""
                if "重新OCR状态" in values:
                    reocr_status = next((v.get("text", "") for v in values["重新OCR状态"] if v.get("text")), "")
                if reocr_status == "已完成":
                    done += 1
            all_done = (done == total and total > 0)
            logging.info(f"[完备性子表] 重新OCR已完成 {done}/{total}")
            csv_upload_state_manager.set_completeness_all_done(all_done)
            if all_done:
                logging.info("[完备性子表] 所有记录已完成！")
            # 字段自动修正逻辑增强
            for record in records:
                values = record.get("values", {})
                record_id = record.get("record_id")
                task_status = [v.get("text", "") for v in values.get("任务状态", [])]
                update_values = {}
                # 任务状态为已完成时，自动补全OCR人员，并将重新OCR状态设为进行中
                task_status = [v.get("text", "") for v in values.get("任务状态", [])]
                reocr_status = [v.get("text", "") for v in values.get("重新OCR状态", [])]
                ocr_persons = [v.get("user_id", v.get("text", "")) for v in values.get("OCR人员", [])]
                
                if "已完成" in task_status:                   # 自动指定OCR人员（如果未指定）
                    if not ocr_user_objs or any(u["user_id"] not in ocr_persons for u in ocr_users):
                        logging.info(f"完备性任务已完成，自动指定OCR人员: record_id={record_id}, 原OCR人员={ocr_persons}")
                        update_values["OCR人员"] = ocr_user_objs
                    
                    # 只有在重新OCR状态不是已完成且不是"进行中"时才设置为"进行中"
                    if "已完成" not in reocr_status and "进行中" not in reocr_status:
                        logging.info(f"完备性任务已完成，设置重新OCR状态为进行中: record_id={record_id}")
                        update_values["重新OCR状态"] = [{"type": "single_select", "text": "进行中"}]
                    # 字段联动时同步更新进度
                    progress_value = calculate_progress_value(values)
                    current_progress = values.get("进度", 0)
                    try:
                        current_progress = int(current_progress)
                    except Exception:
                        current_progress = 0
                    if progress_value > current_progress:
                        update_values["进度"] = progress_value
                if update_values:
                    client.update_records(docid, completeness_sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", [
                        {
                            "record_id": record_id,
                            "values": update_values
                        }
                    ])
                    logging.info(f"已自动修正完备性record_id={record_id}的字段: {list(update_values.keys())}")
            # 如果两个子表都完成，log提示可以上传新文件
            if csv_upload_state_manager.allow_csv_upload:
                logging.info("[系统] 两个子表都已完成，可以上传新文件！")
                # 推送提醒（只推送一次）
                if not csv_upload_state_manager.notified():
                    batch_users = USERS.get("batch_users", [])
                    touser = "|".join([u["user_id"] for u in batch_users])
                    agentid = 1000002
                    csv_name = csv_upload_state_manager.last_uploaded_csv or "-"
                    upload_time = csv_upload_state_manager.last_uploaded_time or "-"
                    msg = f"所有OCR任务已完成，可上传跑批后的csv文件。\n原始上传文件: {csv_name}\n上传时间: {upload_time}"
                    send_wecom_text_message(access_token, agentid, touser, msg)
                    send_wecom_template_card(access_token, agentid, touser, "", f"可上传新csv（{csv_name}）", f"notify_{docid}_{csv_upload_state_manager.round_counter}")
                    logging.info(f"[系统] 已推送可上传新文件提醒给: {touser}")
                    csv_upload_state_manager.mark_notified()
        except Exception as e:
            logging.error(f"完备性监控异常: {e}")
        time.sleep(interval)

def get_access_token(corpid, corpsecret):
    """获取企业微信访问令牌"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取访问令牌失败: {result}")

def create_document(access_token, title):
    """创建企业微信文档，返回docid和url"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_name": title,
        "doc_type": 10  # 10=智能表格
    }
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")

def create_fields_from_mappings(field_mappings):
    for i, mapping in enumerate(field_mappings):
        if "column_index" not in mapping:
            mapping["column_index"] = i
    sorted_mappings = sorted(field_mappings, key=lambda x: x["column_index"])
    reversed_mappings = list(reversed(sorted_mappings))
    fields = []
    for mapping in reversed_mappings:
        field_config = {
            "field_title": mapping["field_title"],
            "field_type": mapping["field_type"]
        }
        if mapping["field_type"] == "FIELD_TYPE_NUMBER" and "property_number" in mapping:
            field_config["property_number"] = mapping["property_number"]
        elif mapping["field_type"] == "FIELD_TYPE_SINGLE_SELECT" and "property_single_select" in mapping:
            field_config["property_single_select"] = mapping["property_single_select"]
        elif mapping["field_type"] == "FIELD_TYPE_SELECT" and "property_select" in mapping:
            field_config["property_select"] = mapping["property_select"]
        elif mapping["field_type"] == "FIELD_TYPE_USER" and "property_user" in mapping:
            field_config["property_user"] = mapping["property_user"]
        elif mapping["field_type"] == "FIELD_TYPE_DATE_TIME" and "property_date_time" in mapping:
            field_config["property_date_time"] = mapping["property_date_time"]
        elif mapping["field_type"] == "FIELD_TYPE_PROGRESS" and "property_progress" in mapping:
            field_config["property_progress"] = mapping["property_progress"]
        fields.append(field_config)
    return fields

def send_wecom_text_message(access_token, agentid, touser, content):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
    data = {
        "touser": touser,  # "@all" 表示全体成员
        "msgtype": "text",
        "agentid": agentid,
        "text": {
            "content": content
        },
        "safe": 0
    }
    resp = requests.post(url, json=data)
    return resp.json()

def send_wecom_template_card(access_token, agentid, touser, docurl, doc_title, task_id):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
    data = {
        "touser": touser,
        "msgtype": "template_card",
        "agentid": agentid,
        "template_card": {
            "card_type": "button_interaction",
            "main_title": {
                "title": "新文档已创建",
                "desc": doc_title
            },
            "sub_title_text": "请及时处理，如需延期可点击下方按钮申请。",
            "horizontal_content_list": [
                {
                    "keyname": "文档链接",
                    "type": 1,
                    "value": "点击查看",
                    "url": docurl
                }
            ],
            "button_list": [
                {
                    "text": "查看文档",
                    "style": 1,
                    "type": 1,
                    "url": docurl
                },
                {
                    "text": "申请延期",
                    "style": 2,
                    "key": "apply_delay"
                }
            ],
            "task_id": task_id
        }
    }
    logging.info(f"[模板卡片] 请求体: {data}")
    resp = requests.post(url, json=data)
    try:
        result = resp.json()
    except Exception as e:
        logging.error(f"[模板卡片] 响应解析失败: {e}, 原始响应: {resp.text}")
        return {"errcode": -1, "errmsg": f"响应解析失败: {e}", "raw": resp.text}
    if result.get("errcode") != 0:
        logging.error(f"[模板卡片] 推送失败: {result}")
    else:
        logging.info(f"[模板卡片] 推送成功: {result}")
    return result

def set_doc_permission(access_token, docid, permission_config):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/mod_doc_join_rule?access_token={access_token}"
    data = {"docid": docid}
    data.update(permission_config)
    resp = requests.post(url, json=data)
    try:
        result = resp.json()
    except Exception as e:
        logging.error(f"[权限设置] 响应解析失败: {e}, 原始响应: {resp.text}")
        return {"errcode": -1, "errmsg": f"响应解析失败: {e}", "raw": resp.text}
    if result.get("errcode") != 0:
        logging.error(f"[权限设置] 设置失败: {result}")
    else:
        logging.info(f"[权限设置] 设置成功: {result}")
    return result

def upload_csv_to_wxwork(csv_file_path, corpid, corpsecret, prev_docid=None):
    access_token = get_access_token(corpid, corpsecret)
    logging.info(f"✅ 获取访问令牌成功")
    processor = CSVDataProcessor()
    logging.info(f"📖 开始解析CSV文件: {csv_file_path}")
    completeness_problems, accuracy_problems, df = processor.extract_problems_from_csv(csv_file_path)
    total_failed_bills = len(df[df['approval_result'] == '不通过']['bill_no'].unique())
    total_records = len(df)
    logging.info(f"✅ 成功解析 {total_records} 条原始记录，{total_failed_bills} 个不通过的借据号")
    logging.info(f"📊 问题统计:")
    logging.info(f"   完备性问题: {len(completeness_problems)} 条")
    logging.info(f"   准确性问题: {len(accuracy_problems)} 条")
    if total_failed_bills == 0:
        logging.info(f"🎉 所有记录均为通过，无需创建企业微信文档。")
        return {
            "success": False,
            "reason": "全部通过，无需创建文档",
            "total_records": total_records,
            "total_failed_bills": total_failed_bills
        }
    doc_title = f"CSV完整报告_{csv_file_path}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, docurl = create_document(access_token, doc_title)
    logging.info(f"📄 创建文档成功，docid: {docid}")
    logging.info(f"🔗 文档链接: {docurl}")
    # 新增：设置文档权限
    set_doc_permission(access_token, docid, DOC_PERMISSION)
    agentid = 1000002
    touser = "@all"
    # 推送模板卡片
    task_id = f"doc_{docid}"
    logging.info(f"准备推送模板卡片... access_token={access_token}, agentid={agentid}, touser={touser}, docurl={docurl}, doc_title={doc_title}, task_id={task_id}")
    card_result = send_wecom_template_card(access_token, agentid, touser, docurl, doc_title, task_id)
    logging.info(f"[模板卡片] 返回: {card_result}")
    # 推送文本消息兜底
    msg = f"新上传的文档已创建，点击查看：{docurl}"
    logging.info(f"准备推送文本消息... access_token={access_token}, agentid={agentid}, touser={touser}, msg={msg}")
    send_result = send_wecom_text_message(access_token, agentid, touser, msg)
    if send_result.get("errcode") != 0:
        logging.error(f"[文本消息] 推送失败: {send_result}")
    else:
        logging.info(f"[文本消息] 推送成功: {send_result}")
    # 5.1 记录上一次文档ID（如有）
    prev_docid_field = None
    if prev_docid:
        prev_docid_field = {"column_index": len(df.columns), "field_title": "上次文档ID", "field_type": "FIELD_TYPE_TEXT"}

    # ========== 移除“客户明细”表格相关代码，仅保留“完备性问题明细” ==========
    # ========== 新增：生成完备性问题明细专用企业微信智能表格 ==========
    detail_title = f"完备性问题明细_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    detail_docid, detail_docurl = create_document(access_token, detail_title)
    logging.info(f"📄 创建完备性问题明细表格成功，docid: {detail_docid}")
    set_doc_permission(access_token, detail_docid, {"enable_corp_external": True, "corp_external_auth": 2, "enable_corp_internal": True, "corp_internal_auth": 2})
    client = WxworkSmartsheetApiClient(access_token)
    add_detail_sheet_result = client.add_sheet(docid=detail_docid, title="完备性问题明细", index=0)
    detail_sheet_id = add_detail_sheet_result.get("properties", {}).get("sheet_id")
    detail_field_mappings = [
        {"column_index": 0, "field_title": "涉及借据号", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 1, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"}
    ]
    detail_fields = create_fields_from_mappings(detail_field_mappings)
    client.add_fields(docid=detail_docid, sheet_id=detail_sheet_id, fields=detail_fields)
    detail_records = []
    for task_name, bill_nos, details in completeness_problems:
        for i, bill_no in enumerate(bill_nos):
            detail = details[i] if i < len(details) else ""
            record = {"values": {}}
            for field in detail_fields:
                if field["field_title"] == "问题详情":
                    record["values"]["问题详情"] = [{"type": "text", "text": detail}]
                elif field["field_title"] == "涉及借据号":
                    record["values"]["涉及借据号"] = [{"type": "text", "text": bill_no}]
            detail_records.append(record)
    batch_size = 100
    for i in range(0, len(detail_records), batch_size):
        batch_records = detail_records[i:i + batch_size]
        client.add_records(
            docid=detail_docid,
            sheet_id=detail_sheet_id,
            key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE",
            records=batch_records
        )
        time.sleep(0.5)
    logging.info(f"✅ 完备性问题明细表格数据上传完成，总共上传 {len(detail_records)} 条记录")

    # ========== 新增：生成准确性问题明细专用企业微信智能表格 ==========
    accuracy_detail_title = f"准确性问题明细_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    accuracy_detail_docid, accuracy_detail_docurl = create_document(access_token, accuracy_detail_title)
    logging.info(f"📄 创建准确性问题明细表格成功，docid: {accuracy_detail_docid}")
    set_doc_permission(access_token, accuracy_detail_docid, {"enable_corp_external": True, "corp_external_auth": 2, "enable_corp_internal": True, "corp_internal_auth": 2})
    accuracy_detail_client = WxworkSmartsheetApiClient(access_token)
    # 按文件类型分组，创建子表
    accuracy_detail_sheet_ids = {}
    for file_type, bill_nos, details in accuracy_problems:
        sheet_title = file_type if file_type else "其他"
        add_sheet_result = accuracy_detail_client.add_sheet(docid=accuracy_detail_docid, title=sheet_title, index=0)
        sheet_id = add_sheet_result.get("properties", {}).get("sheet_id")
        accuracy_detail_sheet_ids[file_type] = sheet_id
        field_mappings = [
            {"column_index": 0, "field_title": "借据号", "field_type": "FIELD_TYPE_TEXT"},
            {"column_index": 1, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"}
        ]
        fields = create_fields_from_mappings(field_mappings)
        accuracy_detail_client.add_fields(docid=accuracy_detail_docid, sheet_id=sheet_id, fields=fields)
        records = []
        for i, bill_no in enumerate(bill_nos):
            detail = details[i] if i < len(details) else file_type
            record = {"values": {}}
            for field in fields:
                if field["field_title"] == "问题详情":
                    record["values"]["问题详情"] = [{"type": "text", "text": detail}]
                elif field["field_title"] == "借据号":
                    record["values"]["借据号"] = [{"type": "text", "text": bill_no}]
            records.append(record)
        for i in range(0, len(records), batch_size):
            batch_records = records[i:i + batch_size]
            accuracy_detail_client.add_records(
                docid=accuracy_detail_docid,
                sheet_id=sheet_id,
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE",
                records=batch_records
            )
            time.sleep(0.5)
    logging.info(f"✅ 准确性问题明细表格数据上传完成，子表数: {len(accuracy_detail_sheet_ids)}")
    # 推送准确性问题明细表格链接
    dev_users = USERS.get("开发人员", [])
    dev_user_ids = [u["user_id"] for u in dev_users]
    touser = "|".join(dev_user_ids)
    msg = f"准确性问题明细下载链接：{accuracy_detail_docurl}"
    send_wecom_text_message(access_token, agentid, touser, msg)
    send_wecom_template_card(access_token, agentid, touser, accuracy_detail_docurl, "准确性问题明细下载", f"accuracy_detail_{accuracy_detail_docid}")
    logging.info(f"[系统] 已推送准确性问题明细链接给: {touser}")

    # ========== 创建主文档下的“完备性问题”子表，并获取completeness_sheet_id ==========
    completeness_sheet_title = "完备性问题"
    add_completeness_sheet_result = client.add_sheet(docid=docid, title=completeness_sheet_title, index=2)
    completeness_sheet_id = add_completeness_sheet_result.get("properties", {}).get("sheet_id")

    # ========== 新增：在最初创建的完备性子表中新增“完备性问题明细链接”字段，并批量写入明细表url ==========
    # 注意：此处操作的completeness_sheet_id为主文档下最初创建的子表
    client.add_fields(docid=docid, sheet_id=completeness_sheet_id, fields=[{"column_index": 99, "field_title": "完备性问题明细链接", "field_type": "FIELD_TYPE_TEXT"}])
    all_records = client.get_records(docid=docid, sheet_id=completeness_sheet_id)
    for record in all_records:
        client.update_records(docid, completeness_sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", [
            {
                "record_id": record["record_id"],
                "values": {"完备性问题明细链接": [{"type": "text", "text": detail_docurl}]}
            }
        ])
    logging.info(f"✅ 主文档完备性子表已插入完备性问题明细链接: {detail_docurl}")

    dev_users = USERS.get("开发人员", [])
    dev_user_ids = [u["user_id"] for u in dev_users]
    touser = "|".join(dev_user_ids)
    msg = f"完备性问题明细下载链接：{detail_docurl}"
    send_wecom_text_message(access_token, agentid, touser, msg)
    send_wecom_template_card(access_token, agentid, touser, detail_docurl, "完备性问题明细下载", f"detail_{detail_docid}")
    logging.info(f"[系统] 已推送完备性问题明细链接给: {touser}")

    # 6. 使用现有的企业微信API客户端
    client = WxworkSmartsheetApiClient(access_token)

    # ========== 第一个表格：原始数据 ==========
    logging.info(f"\n📋 开始创建原始数据表格...")
    original_sheet_title = "原始数据"
    add_original_sheet_result = client.add_sheet(docid=docid, title=original_sheet_title, index=0)
    original_sheet_id = add_original_sheet_result.get("properties", {}).get("sheet_id")
    logging.info(f"📋 创建原始数据子表成功，sheet_id: {original_sheet_id}")
    original_field_mappings = []
    for i, column in enumerate(df.columns):
        original_field_mappings.append({
            "column_index": i,
            "field_title": column,
            "field_type": "FIELD_TYPE_TEXT"
        })
    if prev_docid_field:
        original_field_mappings.append(prev_docid_field)
    original_fields = create_fields_from_mappings(original_field_mappings)
    logging.info(f"📝 添加原始数据字段成功，字段数量: {len(original_fields)}")
    logging.info(f"   字段顺序: {[f['field_title'] for f in original_fields]}")
    client.add_fields(docid=docid, sheet_id=original_sheet_id, fields=original_fields)
    original_records = []
    for _, row in df.iterrows():
        record_values = {}
        for field in original_fields:
            field_title = field['field_title']
            if field_title == "上次文档ID" and prev_docid:
                value = prev_docid
            else:
                value = str(row.get(field_title, ''))
            record_values[field_title] = [{"type": "text", "text": value}]
        record_data = {"values": record_values}
        original_records.append(record_data)
    batch_size = 100
    original_uploaded = 0
    for i in range(0, len(original_records), batch_size):
        batch_records = original_records[i:i + batch_size]
        add_records_result = client.add_records(
            docid=docid, 
            sheet_id=original_sheet_id, 
            key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
            records=batch_records
        )
        batch_count = len(add_records_result.get("records", []))
        original_uploaded += batch_count
        logging.info(f"📤 上传原始数据第 {i//batch_size + 1} 批，{batch_count} 条记录")
        time.sleep(0.5)
    logging.info(f"✅ 原始数据上传完成，总共上传 {original_uploaded} 条记录")

    # ========== 第二个表格：问题汇总 ==========
    logging.info(f"\n📋 开始创建问题汇总表格...")
    problems_sheet_title = "问题汇总"
    add_problems_sheet_result = client.add_sheet(docid=docid, title=problems_sheet_title, index=1)
    problems_sheet_id = add_problems_sheet_result.get("properties", {}).get("sheet_id")
    logging.info(f"📋 创建问题汇总子表成功，sheet_id: {problems_sheet_id}")
    problems_field_mappings = [
        {"column_index": 0, "field_title": "借据号", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 1, "field_title": "问题类型", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 2, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 3, "field_title": "处理时间", "field_type": "FIELD_TYPE_DATE_TIME", "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}}
    ]
    problems_fields = create_fields_from_mappings(problems_field_mappings)
    logging.info(f"📝 添加问题汇总字段成功，字段顺序: {[f['field_title'] for f in problems_fields]}")
    client.add_fields(docid=docid, sheet_id=problems_sheet_id, fields=problems_fields)
    problems_records = []
    
    # 处理完备性问题 - 将聚合数据展开为单个借据号记录
    for task_name, bill_nos, details in completeness_problems:
        for i, bill_no in enumerate(bill_nos):
            detail = details[i] if i < len(details) else ""
            record_data = {
                "values": {
                    "问题详情": [{"type": "text", "text": detail}],
                    "问题类型": [{"type": "text", "text": "完备性"}],
                    "借据号": [{"type": "text", "text": bill_no}]
                }
            }
            problems_records.append(record_data)
    
    # 处理准确性问题 - 将聚合数据展开为单个借据号记录
    for file_type, bill_nos, details in accuracy_problems:
        for i, bill_no in enumerate(bill_nos):
            detail = details[i] if i < len(details) else file_type
            record_data = {
                "values": {
                    "问题详情": [{"type": "text", "text": detail}],
                    "问题类型": [{"type": "text", "text": "准确性"}],
                    "借据号": [{"type": "text", "text": bill_no}]
                }
            }
            problems_records.append(record_data)
    problems_uploaded = 0
    if problems_records:
        for i in range(0, len(problems_records), batch_size):
            batch_records = problems_records[i:i + batch_size]
            add_records_result = client.add_records(
                docid=docid, 
                sheet_id=problems_sheet_id, 
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
                records=batch_records
            )
            batch_count = len(add_records_result.get("records", []))
            problems_uploaded += batch_count
            logging.info(f"📤 上传问题汇总第 {i//batch_size + 1} 批，{batch_count} 条记录")
            time.sleep(0.5)
    else:
        logging.warning(f"⚠️ 没有找到问题数据，跳过问题汇总上传")
    logging.info(f"✅ 问题汇总上传完成，总共上传 {problems_uploaded} 条记录")

    # ========== 第三个表格：完备性问题 ==========
    logging.info(f"\n📋 开始创建完备性问题表格...")
    completeness_sheet_title = "完备性问题"
    add_completeness_sheet_result = client.add_sheet(docid=docid, title=completeness_sheet_title, index=2)
    completeness_sheet_id = add_completeness_sheet_result.get("properties", {}).get("sheet_id")
    logging.info(f"📋 创建完备性问题子表成功，sheet_id: {completeness_sheet_id}")
    completeness_field_mappings = [
        {"column_index": 0, "field_title": "任务名称", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 1, "field_title": "涉及借据号", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 2, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 3, "field_title": "处理时间", "field_type": "FIELD_TYPE_DATE_TIME", "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}},
        {"column_index": 4, "field_title": "处理人员", "field_type": "FIELD_TYPE_USER", "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 5, "field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", "property_single_select": {"options": [
            {"text": "未开始", "style": 18},
            {"text": "进行中", "style": 12},
            {"text": "已完成", "style": 16}
        ]}},
        {"column_index": 6, "field_title": "截止时间", "field_type": "FIELD_TYPE_DATE_TIME", "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}},
        {"column_index": 7, "field_title": "OCR人员", "field_type": "FIELD_TYPE_USER", "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 8, "field_title": "重新OCR状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", "property_single_select": {"options": [
            {"text": "未开始", "style": 18},
            {"text": "进行中", "style": 12},
            {"text": "已完成", "style": 16}
        ]}},
        {"column_index": 9, "field_title": "完备性问题明细链接", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 10, "field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", "property_progress": {"decimal_places": 0}}
    ]
    completeness_fields = create_fields_from_mappings(completeness_field_mappings)
    logging.info(f"📝 添加完备性问题字段成功，字段顺序: {[f['field_title'] for f in completeness_fields]}")
    logging.info(f"🔍 调试：完备性问题字段定义: {completeness_fields}")
    add_fields_result = client.add_fields(docid=docid, sheet_id=completeness_sheet_id, fields=completeness_fields)
    logging.info(f"🔍 调试：add_fields返回结果: {add_fields_result}")
    completeness_records = []
    dev_users = USERS.get("开发人员", [])
    dev_user_objs = [{"type": "user", "user_id": u["user_id"]} for u in dev_users]
    
    # 计算截止时间（当前时间 + 配置的分钟数）
    deadline_timestamp = str(int((datetime.now() + timedelta(minutes=DEADLINE_MINUTES)).timestamp() * 1000))
    
    # 为每个完备性任务创建记录
    for task_name, bill_nos, details in completeness_problems:
        bill_nos_text = ",".join(bill_nos)
        details_text = ";".join(details)
        completeness_records.append({
            "values": {
                "任务名称": [{"type": "text", "text": task_name}],
                "涉及借据号": [{"type": "text", "text": bill_nos_text}],
                "问题详情": [{"type": "text", "text": details_text}],
                "处理人员": dev_user_objs,
                "任务状态": [{"type": "single_select", "text": "未开始"}],
                "截止时间": deadline_timestamp,
                "OCR人员": [],
                "重新OCR状态": [],
                "完备性问题明细链接": [{"type": "text", "text": detail_docurl}],
                "进度": 0
            }
        })
    
    logging.info(f"🔍 调试：筛选出完备性问题任务 {len(completeness_records)} 个")
    if completeness_records:
        logging.info(f"🔍 调试：完备性问题第一条记录: {completeness_records[0]}")
    completeness_uploaded = 0
    if completeness_records:
        for i in range(0, len(completeness_records), batch_size):
            batch_records = completeness_records[i:i + batch_size]
            add_records_result = client.add_records(
                docid=docid, 
                sheet_id=completeness_sheet_id, 
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
                records=batch_records
            )
            batch_count = len(add_records_result.get("records", []))
            completeness_uploaded += batch_count
            logging.info(f"📤 上传完备性问题第 {i//batch_size + 1} 批，{batch_count} 条记录")
            time.sleep(0.5)
    else:
        logging.warning(f"⚠️ 没有找到完备性问题数据，跳过完备性问题上传")
    logging.info(f"✅ 完备性问题上传完成，总共上传 {completeness_uploaded} 条记录")

    # ========== 第四个表格：准确性问题 ==========
    logging.info(f"\n📋 开始创建准确性问题表格...")
    accuracy_sheet_title = "准确性问题"
    add_accuracy_sheet_result = client.add_sheet(docid=docid, title=accuracy_sheet_title, index=3)
    accuracy_sheet_id = add_accuracy_sheet_result.get("properties", {}).get("sheet_id")
    logging.info(f"📋 创建准确性问题子表成功，sheet_id: {accuracy_sheet_id}")
    accuracy_field_mappings = [
        {"column_index": 0, "field_title": "文件类型", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 1, "field_title": "涉及借据号", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 2, "field_title": "问题详情", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 3, "field_title": "处理时间", "field_type": "FIELD_TYPE_DATE_TIME", "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}},
        {"column_index": 4, "field_title": "开发人员", "field_type": "FIELD_TYPE_USER", "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 5, "field_title": "任务原因", "field_type": "FIELD_TYPE_SELECT", "property_select": {"options": [{"text": "影像件模糊"}, {"text": "内容错误"}, {"text": "系统问题"}]}},
        {"column_index": 6, "field_title": "任务名称", "field_type": "FIELD_TYPE_SELECT", "property_select": {"options": [{"text": "调整ocr通过阈值"}, {"text": "与合作机构沟通核对并补传"}, {"text": "修复系统问题"}]}},
        {"column_index": 7, "field_title": "处理人员", "field_type": "FIELD_TYPE_USER", "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 8, "field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", "property_single_select": {"options": [
            {"text": "未开始", "style": 18},
            {"text": "进行中", "style": 12},
            {"text": "已完成", "style": 16}
        ]}},
        {"column_index": 9, "field_title": "截止时间", "field_type": "FIELD_TYPE_DATE_TIME", "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}},
        {"column_index": 10, "field_title": "OCR人员", "field_type": "FIELD_TYPE_USER", "property_user": {"is_multiple": True, "is_notified": True}},
        {"column_index": 11, "field_title": "重新OCR状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", "property_single_select": {"options": [
            {"text": "未开始", "style": 18},
            {"text": "进行中", "style": 12},
            {"text": "已完成", "style": 16}
        ]}},
        {"column_index": 12, "field_title": "准确性问题明细链接", "field_type": "FIELD_TYPE_TEXT"},
        {"column_index": 13, "field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", "property_progress": {"decimal_places": 0}}
    ]
    accuracy_fields = create_fields_from_mappings(accuracy_field_mappings)
    logging.info(f"📝 添加准确性问题字段成功，字段顺序: {[f['field_title'] for f in accuracy_fields]}")
    logging.info(f"🔍 调试：准确性问题字段定义: {accuracy_fields}")
    add_fields_result = client.add_fields(docid=docid, sheet_id=accuracy_sheet_id, fields=accuracy_fields)
    logging.info(f"🔍 调试：add_fields返回结果: {add_fields_result}")
    accuracy_records = []
    dev_users = USERS.get("开发人员", [])
    dev_user_objs = [{"type": "user", "user_id": u["user_id"]} for u in dev_users]
    
    # 计算截止时间（当前时间 + 配置的分钟数）
    deadline_timestamp = str(int((datetime.now() + timedelta(minutes=DEADLINE_MINUTES)).timestamp() * 1000))
    print(f"\n🔍 调试：截止时间戳: {deadline_timestamp}\n")
    # 为每个文件类型的准确性问题创建记录
    for file_type, bill_nos, details in accuracy_problems:
        bill_nos_text = ",".join(bill_nos)
        details_text = ";".join(details)
        accuracy_records.append({
            "values": {
                "文件类型": [{"type": "text", "text": file_type}],
                "涉及借据号": [{"type": "text", "text": bill_nos_text}],
                "问题详情": [{"type": "text", "text": details_text}],
                "开发人员": dev_user_objs,
                "任务原因": [],
                "任务名称": [],
                "处理人员": [],
                "任务状态": [{"type": "single_select", "text": "未开始"}],
                "截止时间": deadline_timestamp,
                "OCR人员": [],
                "重新OCR状态": [],
                "准确性问题明细链接": [{"type": "text", "text": accuracy_detail_docurl}],
                "进度": 0
            }
        })
    
    logging.info(f"🔍 调试：筛选出准确性问题任务 {len(accuracy_records)} 个")
    if accuracy_records:
        logging.info(f"🔍 调试：准确性问题第一条记录: {accuracy_records[0]}")
    accuracy_uploaded = 0
    if accuracy_records:
        for i in range(0, len(accuracy_records), batch_size):
            batch_records = accuracy_records[i:i + batch_size]
            add_records_result = client.add_records(
                docid=docid, 
                sheet_id=accuracy_sheet_id, 
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", 
                records=batch_records
            )
            batch_count = len(add_records_result.get("records", []))
            accuracy_uploaded += batch_count
            logging.info(f"📤 上传准确性问题第 {i//batch_size + 1} 批，{batch_count} 条记录")
            time.sleep(0.5)
    else:
        logging.warning(f"⚠️ 没有找到准确性问题数据，跳过准确性问题上传")
    logging.info(f"✅ 准确性问题上传完成，总共上传 {accuracy_uploaded} 条记录")

    return {
        "success": True,
        "docid": docid,
        "docurl": docurl,
        "original_sheet_id": original_sheet_id,
        "problems_sheet_id": problems_sheet_id,
        "completeness_sheet_id": completeness_sheet_id,
        "accuracy_sheet_id": accuracy_sheet_id,
        "total_records": total_records,
        "total_failed_bills": total_failed_bills,
        "completeness_problems": len(completeness_problems),
        "accuracy_problems": len(accuracy_problems),
        "original_uploaded": original_uploaded,
        "problems_uploaded": problems_uploaded,
        "completeness_uploaded": completeness_uploaded,
        "accuracy_uploaded": accuracy_uploaded,
        "access_token": access_token
    }

def main():
    """
    主函数：支持上传和监控两种功能
    """
    for csv_file in CSV_FILES:
        logging.info(f"\n{'='*60}")
        logging.info(f"🚀 开始处理文件: {csv_file}")
        logging.info(f"{'='*60}")
        result = upload_csv_to_wxwork(csv_file, CORPID, CORPSECRET)
        if result["success"]:
            logging.info(f"🎉 上传成功! 文档ID: {result['docid']},\n 文档链接: {result['docurl']}, 准确性问题子表ID: {result['accuracy_sheet_id']} 完备性问题子表ID: {result['completeness_sheet_id']}")
            access_token = result['access_token']
            # 启动监控功能（准确性+完备性）
            import threading
            t1 = threading.Thread(target=monitor_accuracy_sheet, args=(access_token, result['docid'], result['accuracy_sheet_id'], MONITOR_INTERVAL))
            t2 = threading.Thread(target=monitor_completeness_sheet, args=(access_token, result['docid'], result['completeness_sheet_id'], MONITOR_INTERVAL))
            t1.start()
            t2.start()
            t1.join()
            t2.join()
        else:
            logging.error(f"❌ 上传失败")

if __name__ == "__main__":
    main() 