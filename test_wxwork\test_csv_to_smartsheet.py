import requests
import pandas as pd
import time
from wxwork_smartsheet_api_client import WxworkSmartsheetApiClient
from typing import List, Dict, Any
import json
import threading
import logging

def get_access_token(corpid, corpsecret):
    """获取企业微信 access_token"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    return resp.json().get("access_token")

def create_blank_doc(access_token, title):
    """通过API新建文档，返回docid"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_name": title,
        "doc_type": 10  # 10=智能表格
    }
    resp = requests.post(url, json=data)
    res = resp.json()
    return res.get("docid"), res

def analyze_csv_structure(csv_file_path: str) -> Dict[str, Any]:
    """
    分析CSV文件结构，返回字段信息
    """
    print(f"正在分析CSV文件: {csv_file_path}")
    
    # 读取CSV文件
    df = pd.read_csv(csv_file_path, encoding='utf-8')
    
    # 获取列信息
    columns = df.columns.tolist()
    data_types = df.dtypes.tolist()
    
    print(f"CSV列数: {len(columns)}")
    print(f"CSV行数: {len(df)}")
    print(f"列名: {columns}")
    
    # 分析每列的数据类型，保持原始顺序
    field_mappings = []
    for i, (col_name, dtype) in enumerate(zip(columns, data_types)):
        field_info = {
            "column_index": i,  # 添加列索引以保持顺序
            "column_name": col_name,
            "pandas_dtype": str(dtype),
            "field_title": col_name,
            "field_type": "FIELD_TYPE_TEXT",  # 默认文本类型
            "sample_values": df[col_name].dropna().head(3).tolist()
        }
        
        # 根据数据类型映射到智能表格字段类型
        if 'int' in str(dtype) or 'float' in str(dtype):
            field_info["field_type"] = "FIELD_TYPE_NUMBER"
            field_info["property_number"] = {"decimal_places": 2}
        elif 'datetime' in str(dtype):
            field_info["field_type"] = "FIELD_TYPE_DATE_TIME"
        elif 'bool' in str(dtype):
            field_info["field_type"] = "FIELD_TYPE_CHECKBOX"
        
        field_mappings.append(field_info)
        print(f"列 {i+1}: {col_name} -> {field_info['field_type']}")
    
    return {
        "dataframe": df,
        "field_mappings": field_mappings,
        "total_rows": len(df),
        "total_columns": len(columns),
        "column_order": columns  # 保存原始列顺序
    }

def create_fields_from_mappings(field_mappings: List[Dict]) -> List[Dict]:
    """
    根据字段映射创建API字段配置，反转顺序以匹配CSV列顺序
    """
    # 按column_index排序，然后反转顺序
    sorted_mappings = sorted(field_mappings, key=lambda x: x["column_index"])
    reversed_mappings = list(reversed(sorted_mappings))  # 反转顺序
    
    fields = []
    for mapping in reversed_mappings:
        field_config = {
            "field_title": mapping["field_title"],
            "field_type": mapping["field_type"]
        }
        
        # 添加特定类型的属性
        if mapping["field_type"] == "FIELD_TYPE_NUMBER" and "property_number" in mapping:
            field_config["property_number"] = mapping["property_number"]
        
        fields.append(field_config)
    
    return fields

def convert_row_to_record(row: pd.Series, field_mappings: List[Dict]) -> Dict[str, Any]:
    """
    将pandas行数据转换为API记录格式，反转顺序以匹配CSV列顺序
    """
    values = {}
    
    # 按column_index排序，然后反转顺序
    sorted_mappings = sorted(field_mappings, key=lambda x: x["column_index"])
    reversed_mappings = list(reversed(sorted_mappings))  # 反转顺序
    
    for mapping in reversed_mappings:
        col_name = mapping["column_name"]
        field_title = mapping["field_title"]
        field_type = mapping["field_type"]
        
        cell_value = row[col_name]
        
        # 处理空值
        if pd.isna(cell_value):
            continue
        
        # 根据字段类型格式化数据
        if field_type == "FIELD_TYPE_TEXT":
            values[field_title] = [{"type": "text", "text": str(cell_value)}]
        elif field_type == "FIELD_TYPE_NUMBER":
            values[field_title] = float(cell_value)
        elif field_type == "FIELD_TYPE_DATE_TIME":
            # 转换为时间戳
            if hasattr(cell_value, 'timestamp'):
                timestamp = int(cell_value.timestamp() * 1000)
                values[field_title] = timestamp
            else:
                values[field_title] = [{"type": "text", "text": str(cell_value)}]
        elif field_type == "FIELD_TYPE_CHECKBOX":
            values[field_title] = bool(cell_value)
        else:
            values[field_title] = [{"type": "text", "text": str(cell_value)}]
    
    return {"values": values}

def batch_add_records(client: WxworkSmartsheetApiClient, docid: str, sheet_id: str, 
                     records: List[Dict], batch_size: int = 100) -> List[Dict]:
    """
    分批添加记录，避免单次请求数据过大
    """
    all_results = []
    
    for i in range(0, len(records), batch_size):
        batch = records[i:i + batch_size]
        print(f"正在添加第 {i+1}-{min(i+batch_size, len(records))} 条记录...")
        
        try:
            result = client.add_records(
                docid=docid,
                sheet_id=sheet_id,
                key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE",
                records=batch
            )
            all_results.append(result)
            
            if result.get("errcode") == 0:
                print(f"✓ 成功添加 {len(batch)} 条记录")
            else:
                print(f"✗ 添加记录失败: {result}")
            
            # 避免请求过于频繁
            time.sleep(0.5)
            
        except Exception as e:
            print(f"✗ 添加记录异常: {e}")
            all_results.append({"errcode": -1, "errmsg": str(e)})
    
    return all_results

# TokenManager: 统一管理access_token，定时刷新
class TokenManager:
    def __init__(self, corpid, corpsecret, refresh_interval=1800):
        self.corpid = corpid
        self.corpsecret = corpsecret
        self.refresh_interval = refresh_interval  # 秒，默认30分钟
        self.access_token = None
        self.token_lock = threading.Lock()
        self.logger = logging.getLogger("TokenManager")
        self._stop_event = threading.Event()
        self._thread = threading.Thread(target=self._refresh_token_periodically, daemon=True)
        self._thread.start()

    def _refresh_token_periodically(self):
        while not self._stop_event.is_set():
            try:
                token = get_access_token(self.corpid, self.corpsecret)
                with self.token_lock:
                    self.access_token = token
                self.logger.info(f"access_token已刷新: {token}")
            except Exception as e:
                self.logger.error(f"刷新access_token失败: {e}")
            self._stop_event.wait(self.refresh_interval)

    def get_token(self):
        with self.token_lock:
            return self.access_token

    def stop(self):
        self._stop_event.set()
        self._thread.join()

def csv_to_smartsheet(csv_file_path: str, doc_title: str = None, token_manager: TokenManager = None):
    """
    将CSV文件转换为智能表格
    """
    # 配置信息
    corpid = "wwdb219327d340d664"
    corpsecret = "YLbcAwAfUPe-wwl411yHaVxvl0Yx0sVmlODumWTCGxM"
    logger = logging.getLogger("csv_to_smartsheet")
    logger.info("=== CSV转智能表格开始 ===")

    # 1. 获取access_token
    logger.info("1. 获取access_token...")
    if token_manager:
        access_token = token_manager.get_token()
    else:
        access_token = get_access_token(corpid, corpsecret)
    if not access_token:
        logger.error("✗ 获取access_token失败")
        return
    logger.info("✓ access_token获取成功")
    
    # 2. 分析CSV文件
    print("\n2. 分析CSV文件结构...")
    csv_info = analyze_csv_structure(csv_file_path)
    
    # 3. 创建文档
    print("\n3. 创建智能表格文档...")
    if not doc_title:
        doc_title = f"CSV导入_{int(time.time())}"
    
    docid, doc_create_result = create_blank_doc(access_token, doc_title)
    if not docid:
        print("✗ 创建文档失败:", doc_create_result)
        return
    
    print(f"✓ 文档创建成功: {doc_create_result.get('url')}")
    print(f"docid: {docid}")
    
    # 4. 创建客户端
    client = WxworkSmartsheetApiClient(access_token)
    
    # 5. 创建子表
    print("\n4. 创建子表...")
    add_sheet_result = client.add_sheet(docid=docid, title="CSV数据", index=0)
    sheet_id = add_sheet_result.get("properties", {}).get("sheet_id")
    if not sheet_id:
        print("✗ 创建子表失败:", add_sheet_result)
        return
    
    print(f"✓ 子表创建成功: {sheet_id}")
    
    # 6. 创建字段
    print("\n5. 创建字段...")
    fields = create_fields_from_mappings(csv_info["field_mappings"])
    add_fields_result = client.add_fields(docid=docid, sheet_id=sheet_id, fields=fields)
    
    if add_fields_result.get("errcode") != 0:
        print("✗ 创建字段失败:", add_fields_result)
        return
    
    print(f"✓ 成功创建 {len(fields)} 个字段")
    
    # 7. 转换并添加数据
    print("\n6. 转换并添加数据...")
    records = []
    df = csv_info["dataframe"]
    
    for index, row in df.iterrows():
        record = convert_row_to_record(row, csv_info["field_mappings"])
        records.append(record)
        
        if (index + 1) % 100 == 0:
            print(f"已转换 {index + 1} 行数据...")
    
    print(f"✓ 数据转换完成，共 {len(records)} 条记录")
    
    # 8. 分批添加记录
    print("\n7. 添加数据到智能表格...")
    add_results = batch_add_records(client, docid, sheet_id, records)
    
    # 9. 统计结果
    success_count = 0
    for result in add_results:
        if result.get("errcode") == 0:
            success_count += len(result.get("records", []))
    
    print(f"\n=== 转换完成 ===")
    print(f"文档链接: {doc_create_result.get('url')}")
    print(f"子表ID: {sheet_id}")
    print(f"字段数: {len(fields)}")
    print(f"总记录数: {len(records)}")
    print(f"成功导入: {success_count}")
    print(f"失败记录: {len(records) - success_count}")
    
    return {
        "docid": docid,
        "sheet_id": sheet_id,
        "url": doc_create_result.get('url'),
        "total_records": len(records),
        "success_count": success_count
    }

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s %(name)s: %(message)s')
    # 测试CSV文件路径
    csv_file_path = "example_data2.csv"  # 请替换为你的CSV文件路径
    # 启动TokenManager
    corpid = "wwdb219327d340d664"
    corpsecret = "YLbcAwAfUPe-wwl411yHaVxvl0Yx0sVmlODumWTCGxM"
    token_manager = TokenManager(corpid, corpsecret)
    try:
        result = csv_to_smartsheet(csv_file_path, token_manager=token_manager)
        if result:
            print("\n✓ CSV转智能表格成功完成！")
        else:
            print("\n✗ CSV转智能表格失败！")
    except Exception as e:
        logging.error(f"\n✗ 程序执行异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        token_manager.stop() 