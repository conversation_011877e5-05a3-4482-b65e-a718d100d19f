
# 企业微信配置参数
CORP_ID = "wwdb219327d340d664"  # 在企业微信管理后台获取
TOKEN = "dlPU9ANPXGAPUa35B"  # 自定义的Token
ENCODING_AES_KEY = "EwdIirsucHpgiFFfIU0uN78Ae7zh8bAtXqdFDiJXypv"  # 消息加密Key


import base64
import hashlib
import hmac
import json
import time
import struct
from xml.etree import ElementTree
from urllib.parse import unquote

from Crypto.Cipher import AES
from flask import Flask, request, make_response, Response

app = Flask(__name__)


class WeChatCrypt:
    """企业微信消息加解密工具类"""
    
    def __init__(self, token, encoding_aes_key, corp_id):
        self.token = token
        self.encoding_aes_key = encoding_aes_key
        self.corp_id = corp_id
        # 补全AES Key
        self.aes_key = base64.b64decode(encoding_aes_key + '=') if len(encoding_aes_key) == 43 else None
    
    def get_sha1(self, timestamp, nonce, encrypt):
        """生成签名"""
        try:
            items = [self.token, timestamp, nonce, encrypt]
            items.sort()
            return hashlib.sha1((''.join(items)).encode('utf-8')).hexdigest()
        except Exception as e:
            print("生成签名失败:", str(e))
            raise
    
    def encrypt(self, text, corp_id):
        """加密消息"""
        try:
            # 生成16字节随机字符串
            random = ''.join([chr(ord('a') + i % 26) for i in range(16)])
            # 计算填充长度
            pad = 32 - len(text) % 32
            # 填充内容
            text = random + struct.pack('>I', len(text)) + text.encode('utf-8') + bytes([pad] * pad)
            # 创建AES加密器
            cipher = AES.new(self.aes_key, AES.MODE_CBC, self.aes_key[:16])
            # 加密
            encrypt = cipher.encrypt(text)
            # 拼接CorpID并Base64编码
            return base64.b64encode(encrypt).decode('utf-8')
        except Exception as e:
            print("加密消息失败:", str(e))
            raise
    
    def decrypt(self, encrypt):
        """解密消息"""
        try:
            # Base64解码
            encrypt = base64.b64decode(encrypt)
            # 创建AES解密器
            cipher = AES.new(self.aes_key, AES.MODE_CBC, self.aes_key[:16])
            # 解密
            decrypt = cipher.decrypt(encrypt)
            # 去除填充
            pad = decrypt[-1]
            decrypt = decrypt[:-pad]
            
            # 打印完整解密内容（调试用）
            print(f"完整解密内容: {decrypt}")
            
            # 解析数据
            random = decrypt[:16]  # 前16字节是随机字符串
            msg_len_bytes = decrypt[16:20]  # 接下来4字节是消息长度
            
            # 使用大端字节序解析消息长度
            msg_len = struct.unpack('>I', msg_len_bytes)[0]
            print(f"解析的消息长度: {msg_len}")
            
            # 计算消息和CorpID的位置
            msg_start = 20
            msg_end = msg_start + msg_len
            corp_id_start = msg_end
            
            # 提取消息和CorpID
            msg = decrypt[msg_start:msg_end].decode('utf-8')
            corp_id = decrypt[corp_id_start:].decode('utf-8')
            
            # 打印详细信息
            print(f"随机字符串: {random}")
            print(f"消息内容: {msg}")
            print(f"提取的CorpID: {corp_id}")
            print(f"配置的CorpID: {self.corp_id}")
            
            if corp_id != self.corp_id:
                print(f"CorpID不匹配! 解密:{corp_id}, 配置:{self.corp_id}")
                raise Exception("CorpID不匹配")
                
            return msg
        except Exception as e:
            print("解密消息失败:", str(e))
            raise

@app.route('/wechat', methods=['GET', 'POST'])
def wechat():
    """企业微信消息接收接口"""
    if request.method == 'GET':
        # URL验证逻辑
        try:
            # 获取请求参数
            msg_signature = request.args.get('msg_signature', '')
            timestamp = request.args.get('timestamp', '')
            nonce = request.args.get('nonce', '')
            echostr = request.args.get('echostr', '')
            
            # 对参数进行Urldecode
            msg_signature = unquote(msg_signature)
            timestamp = unquote(timestamp)
            nonce = unquote(nonce)
            echostr = unquote(echostr)
            
            print(f"收到URL验证请求 - msg_signature: {msg_signature}, timestamp: {timestamp}, nonce: {nonce}")
            
            # 验证签名
            wechat_crypt = WeChatCrypt(TOKEN, ENCODING_AES_KEY, CORP_ID)
            expected_signature = wechat_crypt.get_sha1(timestamp, nonce, echostr)
            
            if msg_signature != expected_signature:
                print(f"签名验证失败 - 计算:{expected_signature}, 接收:{msg_signature}")
                return "签名验证失败", 403
            
            # 解密echostr
            decrypted_echostr = wechat_crypt.decrypt(echostr)
            
            # 1秒内返回明文消息内容
            print(f"URL验证成功 - 返回echostr")
            return decrypted_echostr, 200
            
        except Exception as e:
            print(f"URL验证失败: {str(e)}")
            return "验证失败", 400
    
    elif request.method == 'POST':
        # 接收消息逻辑
        try:
            # 获取请求参数
            msg_signature = request.args.get('msg_signature', '')
            timestamp = request.args.get('timestamp', '')
            nonce = request.args.get('nonce', '')
            
            # 获取请求体XML数据
            xml_data = request.data.decode('utf-8')
            root = ElementTree.fromstring(xml_data)
            encrypt = root.find('Encrypt').text
            
            print(f"收到消息 - msg_signature: {msg_signature}, timestamp: {timestamp}, nonce: {nonce}")
            
            # 验证签名
            wechat_crypt = WeChatCrypt(TOKEN, ENCODING_AES_KEY, CORP_ID)
            expected_signature = wechat_crypt.get_sha1(timestamp, nonce, encrypt)
            
            if msg_signature != expected_signature:
                print(f"签名验证失败 - 计算:{expected_signature}, 接收:{msg_signature}")
                return "签名验证失败", 403
            
            # 解密消息
            decrypted_msg = wechat_crypt.decrypt(encrypt)
            print(f"接收到的消息: {decrypted_msg}")
            
            # 这里可以添加自定义消息处理逻辑
            # 示例：回复文本消息
            reply_content = "你发送的消息已收到：" + decrypted_msg
            reply_xml = build_reply_xml(reply_content, timestamp, nonce)
            
            return reply_xml, 200
            
        except Exception as e:
            print(f"接收消息失败: {str(e)}")
            # 直接返回200表示接收成功，不回复消息
            return "", 200

def build_reply_xml(content, timestamp, nonce):
    """构造被动响应XML"""
    wechat_crypt = WeChatCrypt(TOKEN, ENCODING_AES_KEY, CORP_ID)
    
    # 构造回复消息体
    reply_msg = f"""
    <xml>
        <ToUserName><![CDATA[{CORP_ID}]]></ToUserName>
        <FromUserName><![CDATA[你的应用ID]]></FromUserName>
        <CreateTime>{int(time.time())}</CreateTime>
        <MsgType><![CDATA[text]]></MsgType>
        <Content><![CDATA[{content}]]></Content>
    </xml>
    """
    
    # 加密回复消息
    encrypt_msg = wechat_crypt.encrypt(reply_msg, CORP_ID)
    
    # 生成签名
    msg_signature = wechat_crypt.get_sha1(timestamp, nonce, encrypt_msg)
    
    # 构造响应包
    response_xml = f"""
    <xml>
        <Encrypt><![CDATA[{encrypt_msg}]]></Encrypt>
        <MsgSignature><![CDATA[{msg_signature}]]></MsgSignature>
        <TimeStamp>{timestamp}</TimeStamp>
        <Nonce><![CDATA[{nonce}]]></Nonce>
    </xml>
    """
    
    return response_xml.strip()

if __name__ == '__main__':
    # 本地调试使用http，生产环境建议使用https
    app.run(host='0.0.0.0', port=80, debug=True)