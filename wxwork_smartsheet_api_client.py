import requests
from typing import List, Optional, Dict, Any

class WxworkSmartsheetApiClient:
    """
    企业微信 Smartsheet API 客户端，封装了子表和视图的增删改查操作。
    需先获取 access_token。
    """
    BASE_URL = "https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet"

    def __init__(self, access_token: str):
        """
        :param access_token: 企业微信接口调用凭证
        """
        self.access_token = access_token

    def add_sheet(self, docid: str, title: str, index: int) -> Dict[str, Any]:
        """
        新增子表
        :param docid: 文档ID
        :param title: 子表名称
        :param index: 子表索引（插入到第几个位置，从0开始）
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/add_sheet?access_token={self.access_token}"
        data = {
            "docid": docid,
            "properties": {
                "title": title,
                "index": index
            }
        }
        return requests.post(url, json=data).json()

    def delete_sheet(self, docid: str, sheet_id: str) -> Dict[str, Any]:
        """
        删除子表
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/delete_sheet?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id
        }
        return requests.post(url, json=data).json()

    def update_sheet(self, docid: str, sheet_id: str, title: str) -> Dict[str, Any]:
        """
        修改子表名称
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param title: 新子表名称
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/update_sheet?access_token={self.access_token}"
        data = {
            "docid": docid,
            "properties": {
                "sheet_id": sheet_id,
                "title": title
            }
        }
        return requests.post(url, json=data).json()

    def add_view(self, docid: str, sheet_id: str, view_title: str, view_type: str,
                 property_gantt: Optional[dict] = None, property_calendar: Optional[dict] = None) -> Dict[str, Any]:
        """
        新增视图
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param view_title: 视图名称
        :param view_type: 视图类型（如VIEW_TYPE_GRID、VIEW_TYPE_KANBAN、VIEW_TYPE_GALLERY、VIEW_TYPE_GANTT、VIEW_TYPE_CALENDAR）
        :param property_gantt: 甘特图属性，仅添加甘特图时需要
        :param property_calendar: 日历视图属性，仅添加日历视图时需要
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/add_view?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "view_title": view_title,
            "view_type": view_type
        }
        if property_gantt:
            data["property_gantt"] = property_gantt
        if property_calendar:
            data["property_calendar"] = property_calendar
        return requests.post(url, json=data).json()

    def delete_views(self, docid: str, sheet_id: str, view_ids: List[str]) -> Dict[str, Any]:
        """
        删除视图
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param view_ids: 要删除的视图ID列表
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/delete_views?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "view_ids": view_ids
        }
        return requests.post(url, json=data).json()

    def update_view(self, docid: str, sheet_id: str, view_id: str, view_title: str, property: Optional[dict] = None) -> Dict[str, Any]:
        """
        修改视图
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param view_id: 视图ID
        :param view_title: 新视图名称
        :param property: 视图属性（如排序、分组、筛选、字段可见性等，具体结构见官方文档）
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/update_view?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "view_id": view_id,
            "view_title": view_title,
            "property": property or {}
        }
        return requests.post(url, json=data).json()

    def add_fields(self, docid: str, sheet_id: str, fields: list) -> Dict[str, Any]:
        """
        新增字段
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param fields: 字段信息列表，每个字段为dict，需包含field_title、field_type及对应类型的属性
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/add_fields?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "fields": fields
        }
        return requests.post(url, json=data).json()

    def delete_fields(self, docid: str, sheet_id: str, field_ids: list) -> Dict[str, Any]:
        """
        删除字段
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param field_ids: 要删除的字段ID列表
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/delete_fields?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "field_ids": field_ids
        }
        return requests.post(url, json=data).json()

    def update_fields(self, docid: str, sheet_id: str, fields: list) -> Dict[str, Any]:
        """
        修改字段（仅支持自定义字段属性，不能修改字段类型）
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param fields: 字段信息列表，每个字段为dict，需包含field_id、field_title、field_type及对应类型的属性
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/update_fields?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "fields": fields
        }
        return requests.post(url, json=data).json()

    def add_records(self, docid: str, sheet_id: str, key_type: str, records: list) -> Dict[str, Any]:
        """
        新增记录
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param key_type: 字段key类型（CELL_VALUE_KEY_TYPE_FIELD_TITLE 或 CELL_VALUE_KEY_TYPE_FIELD_ID）
        :param records: 要新增的记录列表，每条记录为dict，values为字段名/ID与值的映射
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/add_records?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "key_type": key_type,
            "records": records
        }
        return requests.post(url, json=data).json()

    def delete_records(self, docid: str, sheet_id: str, record_ids: list) -> Dict[str, Any]:
        """
        删除记录
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param record_ids: 要删除的记录ID列表
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/delete_records?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "record_ids": record_ids
        }
        return requests.post(url, json=data).json()

    def update_records(self, docid: str, sheet_id: str, key_type: str, records: list) -> Dict[str, Any]:
        """
        修改记录
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param key_type: 字段key类型（CELL_VALUE_KEY_TYPE_FIELD_TITLE 或 CELL_VALUE_KEY_TYPE_FIELD_ID）
        :param records: 要修改的记录列表，每条记录为dict，需包含record_id和values
        :return: API返回的JSON数据
        """
        url = f"{self.BASE_URL}/update_records?access_token={self.access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "key_type": key_type,
            "records": records
        }
        return requests.post(url, json=data).json()

    def get_records(self, docid: str, sheet_id: str, offset: int = 0, limit: int = 1000) -> list:
        """
        获取子表所有记录（按字段名返回）
        :param docid: 文档ID
        :param sheet_id: 子表ID
        :param offset: 分页起始位置
        :param limit: 每页数量，最大1000
        :return: 记录列表（每条为dict，含record_id和values）
        """
        url = f"{self.BASE_URL}/get_records?access_token={self.access_token}"
        all_records = []
        while True:
            data = {
                "docid": docid,
                "sheet_id": sheet_id,
                "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
                "offset": offset,
                "limit": limit
            }
            resp = requests.post(url, json=data).json()
            if resp.get("errcode") != 0:
                raise Exception(f"获取记录失败: {resp}")
            records = resp.get("records", [])
            all_records.extend(records)
            has_more = resp.get("has_more", False)
            if not has_more or not records:
                break
            offset = resp.get("next", offset + limit)
        return all_records 