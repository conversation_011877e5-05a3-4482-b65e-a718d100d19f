import requests

def get_access_token(corpid, corpsecret):
    """
    获取企业微信 access_token
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    return resp.json().get("access_token")

def create_wxwork_doc(access_token, doc_name, doc_type=3, spaceid=None, fatherid=None, admin_users=None):
    """
    创建企业微信文档
    :param access_token: 企业微信 access_token
    :param doc_name: 文档名称
    :param doc_type: 文档类型（3:文档 4:表格 10:智能表格）
    :param spaceid: 空间ID（可选）
    :param fatherid: 父目录ID（可选）
    :param admin_users: 文档管理员userid列表（可选）
    :return: 接口返回的json
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_type": doc_type,
        "doc_name": doc_name
    }
    if spaceid:
        data["spaceid"] = spaceid
    if fatherid:
        data["fatherid"] = fatherid
    if admin_users:
        data["admin_users"] = admin_users
    resp = requests.post(url, json=data)
    return resp.json()

if __name__ == "__main__":
    # 请替换为你自己的企业ID和应用Secret
    corpid = "wwdb219327d340d664"
    corpsecret = "YLbcAwAfUPe-wwl411yHaVxvl0Yx0sVmlODumWTCGxM"
    access_token = get_access_token(corpid, corpsecret)
    result = create_wxwork_doc(access_token, doc_name="测试智恩表格", doc_type=10)
    print(result)
