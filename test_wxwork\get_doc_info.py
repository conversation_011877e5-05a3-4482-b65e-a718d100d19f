import requests

def get_access_token(corpid, corpsecret):
    """
    获取企业微信 access_token
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    return resp.json().get("access_token")

def get_wxwork_doc_info(access_token, docid):
    """
    获取企业微信文档基础信息（包括智能表格）
    :param access_token: 企业微信 access_token
    :param docid: 文档ID
    :return: 接口返回的json
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/get_doc_base_info?access_token={access_token}"
    data = {"docid": docid}
    resp = requests.post(url, json=data)
    print("HTTP状态码：", resp.status_code)
    print("返回内容：", repr(resp.text))
    try:
        return resp.json()
    except Exception as e:
        print("解析JSON出错：", e)
        return None

if __name__ == "__main__":
    # 请替换为你自己的企业ID和应用Secret
    corpid = "wwdb219327d340d664"
    corpsecret = "YLbcAwAfUPe-wwl411yHaVxvl0Yx0sVmlODumWTCGxM"
    access_token = get_access_token(corpid, corpsecret)
    # 请替换为你实际创建的智能表格docid
    docid = "dcrte-z3XkPGLtfNJbBMAgaPOMrh57UAtkfCJYR8GtEQtAHEdaJml5mlvq_hWV4RVZfNQIYxV2KRrBibHseWhuJA"
    result = get_wxwork_doc_info(access_token, docid)
    print(result)