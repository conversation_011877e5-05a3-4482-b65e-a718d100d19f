#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试超时提醒功能
"""

import json
import requests
import logging
import time
import threading
from datetime import datetime, timedelta
from wxwork_smartsheet_api_client import WxworkSmartsheetApiClient

logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s: %(message)s")

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

CORPID = config["corpid"]
CORPSECRET = config["corpsecret"]
USERS = config.get("users", {})

def get_access_token(corpid, corpsecret):
    """获取企业微信访问令牌"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("access_token")
    else:
        raise Exception(f"获取access_token失败: {result}")

def create_document(access_token, title):
    """创建测试文档"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {"doc_name": title, "doc_type": 10}
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result.get("docid"), result.get("url")
    else:
        raise Exception(f"创建文档失败: {result}")

def add_sheet(access_token, docid, title, index=0):
    """添加子表"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
    data = {"docid": docid, "title": title, "index": index}
    resp = requests.post(url, json=data)
    result = resp.json()
    if result.get("errcode") == 0:
        return result["properties"]["sheet_id"]
    else:
        raise Exception(f"添加子表失败: {result}")

def send_wecom_text_message(access_token, agentid, touser, content):
    """发送企业微信文本消息"""
    url = f"https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={access_token}"
    data = {
        "touser": touser,
        "msgtype": "text",
        "agentid": agentid,
        "text": {"content": content},
        "safe": 0
    }
    resp = requests.post(url, json=data)
    return resp.json()

def create_test_tasks_with_deadline(access_token, docid, sheet_id):
    """创建带截止时间的测试任务"""
    client = WxworkSmartsheetApiClient(access_token)
    
    # 添加字段
    fields = [
        {"field_title": "任务名称", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "处理人员", "field_type": "FIELD_TYPE_USER", 
         "property_user": {"is_multiple": True, "is_notified": True}},
        {"field_title": "任务状态", "field_type": "FIELD_TYPE_SINGLE_SELECT", 
         "property_single_select": {"options": [
             {"text": "未开始", "style": 18},
             {"text": "进行中", "style": 12},
             {"text": "已完成", "style": 16}
         ]}},
        {"field_title": "截止时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": False}},
        {"field_title": "创建时间", "field_type": "FIELD_TYPE_DATE_TIME", 
         "property_date_time": {"format": "yyyy-mm-dd hh:mm", "auto_fill": True}}
    ]
    
    client.add_fields(docid, sheet_id, fields)
    logging.info("✅ 添加字段成功")
    
    # 创建不同截止时间的测试任务
    now = datetime.now()
    dev_users = USERS.get("开发人员", [])
    dev_user_objs = [{"type": "user", "user_id": u["user_id"]} for u in dev_users]
    
    test_tasks = [
        {
            "name": "测试任务1-已超时",
            "deadline": now - timedelta(minutes=5),  # 5分钟前（已超时）
            "status": "进行中"
        },
        {
            "name": "测试任务2-即将超时",
            "deadline": now + timedelta(minutes=2),  # 2分钟后（即将超时）
            "status": "未开始"
        },
        {
            "name": "测试任务3-正常时间",
            "deadline": now + timedelta(minutes=30), # 30分钟后（正常）
            "status": "未开始"
        },
        {
            "name": "测试任务4-已完成",
            "deadline": now - timedelta(minutes=10), # 10分钟前但已完成
            "status": "已完成"
        }
    ]
    
    records = []
    for task in test_tasks:
        deadline_ms = int(task["deadline"].timestamp() * 1000)
        records.append({
            "values": {
                "任务名称": [{"type": "text", "text": task["name"]}],
                "处理人员": dev_user_objs,
                "任务状态": [{"type": "single_select", "text": task["status"]}],
                "截止时间": deadline_ms
            }
        })
    
    result = client.add_records(docid, sheet_id, "CELL_VALUE_KEY_TYPE_FIELD_TITLE", records)
    logging.info(f"✅ 创建测试任务成功: {len(records)} 个任务")
    return result

def check_timeout_tasks(access_token, docid, sheet_id):
    """检测超时任务"""
    client = WxworkSmartsheetApiClient(access_token)
    records = client.get_records(docid, sheet_id)
    
    now_ms = int(datetime.now().timestamp() * 1000)
    timeout_tasks = []
    warning_tasks = []  # 即将超时的任务
    
    for record in records:
        values = record.get("values", {})
        record_id = record.get("record_id")
        
        # 获取任务信息
        task_name = ""
        if "任务名称" in values and values["任务名称"]:
            task_name = values["任务名称"][0].get("text", "")
        
        # 获取任务状态
        task_status = ""
        if "任务状态" in values and values["任务状态"]:
            task_status = values["任务状态"][0].get("text", "")
        
        # 获取截止时间
        deadline_ms = None
        if "截止时间" in values and values["截止时间"]:
            deadline_ms = values["截止时间"]
        
        # 获取处理人员
        assignees = []
        if "处理人员" in values and values["处理人员"]:
            assignees = [user.get("user_id", "") for user in values["处理人员"]]
        
        if deadline_ms and task_status != "已完成":
            try:
                deadline_ms = int(deadline_ms) if isinstance(deadline_ms, str) else deadline_ms
                time_diff_ms = now_ms - deadline_ms
                time_diff_minutes = time_diff_ms / (1000 * 60)
                
                task_info = {
                    "record_id": record_id,
                    "task_name": task_name,
                    "task_status": task_status,
                    "deadline_ms": deadline_ms,
                    "assignees": assignees,
                    "overdue_minutes": time_diff_minutes
                }
                
                if time_diff_minutes > 0:  # 已超时
                    timeout_tasks.append(task_info)
                elif time_diff_minutes > -5:  # 5分钟内即将超时
                    warning_tasks.append(task_info)
                    
            except (ValueError, TypeError) as e:
                logging.warning(f"解析截止时间失败: {deadline_ms}, 错误: {e}")
    
    return timeout_tasks, warning_tasks

def generate_timeout_report(timeout_tasks, warning_tasks):
    """生成超时任务报告"""
    report = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "timeout_count": len(timeout_tasks),
        "warning_count": len(warning_tasks),
        "timeout_details": [],
        "warning_details": []
    }

    for task in timeout_tasks:
        report["timeout_details"].append({
            "task_name": task["task_name"],
            "status": task["task_status"],
            "overdue_hours": round(task["overdue_minutes"] / 60, 1),
            "assignees": task["assignees"]
        })

    for task in warning_tasks:
        report["warning_details"].append({
            "task_name": task["task_name"],
            "status": task["task_status"],
            "remaining_minutes": round(-task["overdue_minutes"], 0),
            "assignees": task["assignees"]
        })

    return report

def send_timeout_reminder(access_token, timeout_tasks, warning_tasks):
    """发送超时提醒消息"""
    agentid = 1000002

    # 生成报告
    report = generate_timeout_report(timeout_tasks, warning_tasks)
    logging.info(f"📊 超时报告: {report}")

    # 发送超时任务提醒
    if timeout_tasks:
        timeout_msg_lines = ["🚨 超时任务提醒："]
        timeout_msg_lines.append(f"检查时间: {report['timestamp']}")
        timeout_msg_lines.append("")

        for task in timeout_tasks:
            overdue_hours = task["overdue_minutes"] / 60
            assignee_names = ", ".join(task["assignees"]) if task["assignees"] else "未分配"
            timeout_msg_lines.append(
                f"• {task['task_name']}\n  状态: {task['task_status']}\n  负责人: {assignee_names}\n  超时: {overdue_hours:.1f}小时"
            )
            timeout_msg_lines.append("")

            # 给任务负责人发送个人提醒
            for assignee in task["assignees"]:
                personal_msg = f"🚨 任务超时提醒\n\n任务: {task['task_name']}\n状态: {task['task_status']}\n超时时间: {overdue_hours:.1f}小时\n\n请尽快处理！"
                result = send_wecom_text_message(access_token, agentid, assignee, personal_msg)
                logging.info(f"✅ 发送个人超时提醒给 {assignee}: {result.get('errmsg', 'unknown')}")

        # 发送汇总消息给管理员
        batch_users = USERS.get("batch_users", [])
        if batch_users:
            touser = "|".join([u["user_id"] for u in batch_users])
            timeout_msg = "\n".join(timeout_msg_lines)
            result = send_wecom_text_message(access_token, agentid, touser, timeout_msg)
            logging.info(f"✅ 发送超时汇总提醒: {result.get('errmsg', 'unknown')}")

    # 发送即将超时任务提醒
    if warning_tasks:
        warning_msg_lines = ["⚠️ 即将超时任务提醒："]
        warning_msg_lines.append(f"检查时间: {report['timestamp']}")
        warning_msg_lines.append("")

        for task in warning_tasks:
            remaining_minutes = -task["overdue_minutes"]
            assignee_names = ", ".join(task["assignees"]) if task["assignees"] else "未分配"
            warning_msg_lines.append(
                f"• {task['task_name']}\n  状态: {task['task_status']}\n  负责人: {assignee_names}\n  剩余: {remaining_minutes:.0f}分钟"
            )
            warning_msg_lines.append("")

            # 给任务负责人发送个人提醒
            for assignee in task["assignees"]:
                personal_msg = f"⚠️ 任务即将超时提醒\n\n任务: {task['task_name']}\n状态: {task['task_status']}\n剩余时间: {remaining_minutes:.0f}分钟\n\n请抓紧处理！"
                result = send_wecom_text_message(access_token, agentid, assignee, personal_msg)
                logging.info(f"✅ 发送个人即将超时提醒给 {assignee}: {result.get('errmsg', 'unknown')}")

        # 发送汇总消息给管理员
        batch_users = USERS.get("batch_users", [])
        if batch_users:
            touser = "|".join([u["user_id"] for u in batch_users])
            warning_msg = "\n".join(warning_msg_lines)
            result = send_wecom_text_message(access_token, agentid, touser, warning_msg)
            logging.info(f"✅ 发送即将超时汇总提醒: {result.get('errmsg', 'unknown')}")

    return report

def monitor_timeout_tasks(access_token, docid, sheet_id, check_interval=30):
    """超时任务监控线程"""
    logging.info(f"🔍 开始超时任务监控，每{check_interval}秒检查一次...")
    
    while True:
        try:
            timeout_tasks, warning_tasks = check_timeout_tasks(access_token, docid, sheet_id)
            
            logging.info(f"📊 超时检查结果: 超时任务 {len(timeout_tasks)} 个, 即将超时任务 {len(warning_tasks)} 个")
            
            if timeout_tasks:
                logging.warning(f"🚨 发现超时任务:")
                for task in timeout_tasks:
                    logging.warning(f"  - {task['task_name']} (超时 {task['overdue_minutes']:.1f} 分钟)")
            
            if warning_tasks:
                logging.info(f"⚠️ 发现即将超时任务:")
                for task in warning_tasks:
                    logging.info(f"  - {task['task_name']} (剩余 {-task['overdue_minutes']:.1f} 分钟)")
            
            # 发送提醒消息
            if timeout_tasks or warning_tasks:
                send_timeout_reminder(access_token, timeout_tasks, warning_tasks)
            
        except Exception as e:
            logging.error(f"超时监控异常: {e}")
        
        time.sleep(check_interval)

def test_timeout_reminder():
    """测试超时提醒功能"""
    logging.info("🚀 开始测试超时提醒功能...")
    
    # 获取访问令牌
    access_token = get_access_token(CORPID, CORPSECRET)
    logging.info("✅ 获取访问令牌成功")
    
    # 创建测试文档
    doc_title = f"超时提醒测试_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    docid, doc_url = create_document(access_token, doc_title)
    logging.info(f"✅ 创建测试文档成功: {doc_url}")
    
    # 创建子表
    sheet_id = add_sheet(access_token, docid, "超时测试任务", 0)
    logging.info(f"✅ 创建子表成功: {sheet_id}")
    
    # 创建测试任务
    create_test_tasks_with_deadline(access_token, docid, sheet_id)
    
    # 立即检查一次超时任务
    logging.info("🔍 立即检查超时任务...")
    timeout_tasks, warning_tasks = check_timeout_tasks(access_token, docid, sheet_id)
    
    logging.info(f"📊 检查结果:")
    logging.info(f"  超时任务: {len(timeout_tasks)} 个")
    logging.info(f"  即将超时任务: {len(warning_tasks)} 个")
    
    # 发送测试提醒
    if timeout_tasks or warning_tasks:
        logging.info("📤 发送测试提醒消息...")
        send_timeout_reminder(access_token, timeout_tasks, warning_tasks)
    
    # 启动持续监控（可选）
    print(f"\n🎯 测试完成！")
    print(f"📋 测试文档: {doc_url}")
    print(f"💡 功能验证:")
    print(f"   1. 检查企业微信是否收到超时提醒消息")
    print(f"   2. 观察不同类型任务的提醒内容")
    print(f"   3. 验证个人提醒和汇总提醒")
    
    # 询问是否启动持续监控
    start_monitor = input("\n是否启动持续监控？(y/n): ").lower().strip()
    if start_monitor == 'y':
        logging.info("🔄 启动持续监控...")
        monitor_thread = threading.Thread(
            target=monitor_timeout_tasks, 
            args=(access_token, docid, sheet_id, 30)
        )
        monitor_thread.daemon = True
        monitor_thread.start()
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logging.info("👋 监控已停止")
    
    return docid, doc_url

if __name__ == "__main__":
    print("⏰ 超时提醒功能测试")
    print("=" * 40)
    
    try:
        docid, doc_url = test_timeout_reminder()
        print(f"\n✅ 测试完成")
        print(f"📋 文档链接: {doc_url}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
