import requests
from wxwork_smartsheet_api_client import WxworkSmartsheetApiClient
import time

def get_access_token(corpid, corpsecret):
    url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
    resp = requests.get(url)
    return resp.json().get("access_token")

# 新建文档API（如无API请手动新建并填写docid）
def create_blank_doc(access_token, title):
    """
    通过API新建文档，返回docid。若无API请手动新建。
    """
    url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
    data = {
        "doc_name": title,
        "doc_type": 10  # 10=智能表格
    }
    resp = requests.post(url, json=data)
    res = resp.json()
    return res.get("docid"), res

if __name__ == "__main__":
    corpid = "wwdb219327d340d664"
    corpsecret = "YLbcAwAfUPe-wwl411yHaVxvl0Yx0sVmlODumWTCGxM"
    access_token = get_access_token(corpid, corpsecret)
    print("access_token:", access_token)

    # 1. 新建文档（如无API请手动新建并填写docid）
    doc_title = f"API自动化测试表格_{int(time.time())}"
    docid, doc_create_result = create_blank_doc(access_token, doc_title)
    print("新建文档结果:", doc_create_result)
    if not docid:
        print("未能自动新建文档，请手动新建并填写docid后重试。")
        exit(1)
    print("docid:", docid)

    client = WxworkSmartsheetApiClient(access_token)

    # 2. 新建子表
    add_sheet_result = client.add_sheet(docid=docid, title="自动化子表", index=0)
    print("add_sheet_result:", add_sheet_result)
    sheet_id = add_sheet_result.get("properties", {}).get("sheet_id")
    if not sheet_id:
        print("未能获取sheet_id，退出测试。")
        exit(1)
    print("sheet_id:", sheet_id)

    # 3. 新增字段
    fields = [
        {"field_title": "文本字段", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "数字字段", "field_type": "FIELD_TYPE_NUMBER", "property_number": {"decimal_places": 2}},
        {"field_title": "备注", "field_type": "FIELD_TYPE_TEXT"}
    ]
    add_fields_result = client.add_fields(docid=docid, sheet_id=sheet_id, fields=fields)
    print("add_fields_result:", add_fields_result)
    field_ids = [f.get("field_id") for f in add_fields_result.get("fields", [])]
    print("字段ID列表:", field_ids)

    # 4. 修改字段
    if field_ids:
        update_fields = [
            {"field_id": field_ids[0], "field_title": "文本字段_已修改", "field_type": "FIELD_TYPE_TEXT"}
        ]
        update_fields_result = client.update_fields(docid=docid, sheet_id=sheet_id, fields=update_fields)
        print("update_fields_result:", update_fields_result)

    # 5. 新增记录 - 修复数字字段格式
    records = [
        {
            "values": {
                "文本字段_已修改": [{"type": "text", "text": "第一行内容"}], 
                "数字字段": 123.45,  # 直接使用数字，不是数组
                "备注": [{"type": "text", "text": "自动化备注"}]
            }
        },
        {
            "values": {
                "文本字段_已修改": [{"type": "text", "text": "第二行内容"}], 
                "数字字段": 678.90,  # 直接使用数字，不是数组
                "备注": [{"type": "text", "text": "第二行备注"}]
            }
        }
    ]
    add_records_result = client.add_records(docid=docid, sheet_id=sheet_id, key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", records=records)
    print("add_records_result:", add_records_result)
    record_ids = [r.get("record_id") for r in add_records_result.get("records", []) if r.get("record_id")]
    print("记录ID列表:", record_ids)

    # 6. 修改记录
    if record_ids:
        update_records = [
            {"record_id": record_ids[0], "values": {"备注": [{"type": "text", "text": "已修改备注"}]}}
        ]
        update_records_result = client.update_records(docid=docid, sheet_id=sheet_id, key_type="CELL_VALUE_KEY_TYPE_FIELD_TITLE", records=update_records)
        print("update_records_result:", update_records_result)

    # 7. 新增视图
    add_view_result = client.add_view(docid=docid, sheet_id=sheet_id, view_title="自动化视图", view_type="VIEW_TYPE_GRID")
    print("add_view_result:", add_view_result)
    view_id = add_view_result.get("view", {}).get("view_id")

    # 8. 修改视图 - 设置字段可见性
    if view_id and field_ids:
        # 设置所有字段为可见
        field_visibility = {}
        for field_id in field_ids:
            field_visibility[field_id] = True
        
        view_property = {
            "field_visibility": field_visibility,
            "frozen_field_count": 0
        }
        update_view_result = client.update_view(
            docid=docid, 
            sheet_id=sheet_id, 
            view_id=view_id, 
            view_title="自动化视图_已修改",
            property=view_property
        )
        print("update_view_result:", update_view_result)

    print("\n=== 测试完成 ===")
    print(f"文档链接: {doc_create_result.get('url')}")
    print(f"子表ID: {sheet_id}")
    print(f"字段ID: {field_ids}")
    print(f"记录ID: {record_ids}")
    print("请在企业微信文档中查看表格内容变化。")
    print("注意：所有测试数据已保留，如需清理请手动删除。")