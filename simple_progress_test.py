#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的进度字段测试脚本
"""

import json
import requests
import time

# 读取配置
try:
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    print("✅ 配置文件读取成功")
except Exception as e:
    print(f"❌ 配置文件读取失败: {e}")
    exit(1)

CORPID = config['corpid']
CORPSECRET = config['corpsecret']

def get_access_token(corpid, corpsecret):
    """获取企业微信访问令牌"""
    try:
        url = f"https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={corpid}&corpsecret={corpsecret}"
        response = requests.get(url)
        result = response.json()
        if result.get('errcode') == 0:
            print("✅ 访问令牌获取成功")
            return result.get('access_token')
        else:
            print(f"❌ 获取access_token失败: {result}")
            return None
    except Exception as e:
        print(f"❌ 获取访问令牌异常: {e}")
        return None

def create_test_document(access_token):
    """创建测试文档"""
    try:
        url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/create_doc?access_token={access_token}"
        data = {
            "doc_type": 4,  # smartsheet类型
            "doc_name": f"进度字段简单测试_{int(time.time())}"
        }
        response = requests.post(url, json=data)
        result = response.json()
        if result.get('errcode') == 0:
            print("✅ 测试文档创建成功")
            return result.get('docid'), result.get('url')
        else:
            print(f"❌ 创建文档失败: {result}")
            return None, None
    except Exception as e:
        print(f"❌ 创建文档异常: {e}")
        return None, None

def add_sheet(access_token, docid, title):
    """添加子表"""
    try:
        url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_sheet?access_token={access_token}"
        data = {
            "docid": docid,
            "properties": {
                "title": title,
                "index": 0
            }
        }
        response = requests.post(url, json=data)
        result = response.json()
        if result.get('errcode') == 0:
            sheet_id = result.get("properties", {}).get("sheet_id")
            print(f"✅ 子表创建成功，sheet_id: {sheet_id}")
            return sheet_id
        else:
            print(f"❌ 创建子表失败: {result}")
            return None
    except Exception as e:
        print(f"❌ 创建子表异常: {e}")
        return None

def add_fields(access_token, docid, sheet_id, fields):
    """添加字段"""
    try:
        url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_fields?access_token={access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "fields": fields
        }
        response = requests.post(url, json=data)
        result = response.json()
        print(f"📝 添加字段结果: {result}")
        return result
    except Exception as e:
        print(f"❌ 添加字段异常: {e}")
        return None

def add_records(access_token, docid, sheet_id, records):
    """添加记录"""
    try:
        url = f"https://qyapi.weixin.qq.com/cgi-bin/wedoc/smartsheet/add_records?access_token={access_token}"
        data = {
            "docid": docid,
            "sheet_id": sheet_id,
            "key_type": "CELL_VALUE_KEY_TYPE_FIELD_TITLE",
            "records": records
        }
        response = requests.post(url, json=data)
        result = response.json()
        print(f"📤 添加记录结果: {result}")
        return result
    except Exception as e:
        print(f"❌ 添加记录异常: {e}")
        return None

def test_progress_formats():
    """测试进度字段格式"""
    print("🚀 开始简化的进度字段测试...")
    
    # 1. 获取访问令牌
    access_token = get_access_token(CORPID, CORPSECRET)
    if not access_token:
        return
    
    # 2. 创建测试文档
    docid, doc_url = create_test_document(access_token)
    if not docid:
        return
    
    print(f"📋 文档链接: {doc_url}")
    
    # 3. 创建子表
    sheet_id = add_sheet(access_token, docid, "进度测试")
    if not sheet_id:
        return
    
    # 4. 添加字段
    fields = [
        {"field_title": "任务名称", "field_type": "FIELD_TYPE_TEXT"},
        {"field_title": "进度", "field_type": "FIELD_TYPE_PROGRESS", "property_progress": {"decimal_places": 0}}
    ]
    
    add_fields_result = add_fields(access_token, docid, sheet_id, fields)
    if not add_fields_result or add_fields_result.get('errcode') != 0:
        print("❌ 字段添加失败，停止测试")
        return
    
    # 5. 测试不同的进度格式
    print("\n🔍 测试格式1: 当前代码使用的格式")
    records_format1 = [
        {
            "values": {
                "任务名称": [{"type": "text", "text": "格式1测试"}],
                "进度": [{"type": "number", "number": 25}]  # 当前代码格式
            }
        }
    ]
    add_records(access_token, docid, sheet_id, records_format1)
    
    print("\n🔍 测试格式2: API文档建议的格式")
    records_format2 = [
        {
            "values": {
                "任务名称": [{"type": "text", "text": "格式2测试"}],
                "进度": 50  # API文档格式
            }
        }
    ]
    add_records(access_token, docid, sheet_id, records_format2)
    
    print("\n🔍 测试格式3: 数组包装的数值")
    records_format3 = [
        {
            "values": {
                "任务名称": [{"type": "text", "text": "格式3测试"}],
                "进度": [75]  # 数组包装的数值
            }
        }
    ]
    add_records(access_token, docid, sheet_id, records_format3)
    
    print(f"\n🎯 测试完成！")
    print(f"📋 请查看文档: {doc_url}")
    print(f"💡 观察不同格式的进度字段在企业微信中的显示效果")
    
    return doc_url

if __name__ == "__main__":
    print("⚡ 简化的进度字段测试工具")
    print("=" * 40)
    
    try:
        doc_url = test_progress_formats()
        if doc_url:
            print(f"\n✅ 测试成功完成")
            print(f"📋 文档链接: {doc_url}")
        else:
            print(f"\n❌ 测试失败")
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
