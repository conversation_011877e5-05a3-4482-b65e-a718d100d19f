from fastapi import Fast<PERSON><PERSON>, File, UploadFile
from fastapi.responses import JSONResponse
import shutil
import os
import json
import threading
from csv_uploader_with_monitor import monitor_accuracy_sheet, monitor_completeness_sheet, CORPID, CORPSECRET, MONITOR_INTERVAL, upload_csv_to_wxwork, csv_upload_state_manager

app = FastAPI()

# 读取配置
with open('config.json', 'r', encoding='utf-8') as f:
    config = json.load(f)

CSV_UPLOAD_CONFIG = config.get("csv_upload", {
    "initial_csv": "ocr_upload/initial_latest.csv",
    "completeness_csv": "ocr_upload/completeness_latest.csv",
    "accuracy_csv": "ocr_upload/accuracy_latest.csv"
})

# 上传初始CSV并自动触发main处理
def run_main_for_initial_csv(csv_path):
    result = upload_csv_to_wxwork(csv_path, CORPID, CORPSECRET)
    # 自动启动监控（仅在创建文档成功时）
    if result and result.get("success"):
        access_token = result["access_token"]
        docid = result["docid"]
        accuracy_sheet_id = result["accuracy_sheet_id"]
        completeness_sheet_id = result["completeness_sheet_id"]
        import threading
        t1 = threading.Thread(target=monitor_accuracy_sheet, args=(access_token, docid, accuracy_sheet_id, MONITOR_INTERVAL))
        t2 = threading.Thread(target=monitor_completeness_sheet, args=(access_token, docid, completeness_sheet_id, MONITOR_INTERVAL))
        t1.start()
        t2.start()

@app.post("/upload_initial_csv")
async def upload_initial_csv(file: UploadFile = File(...)):
    save_path = CSV_UPLOAD_CONFIG["initial_csv"]
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    with open(save_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    # 异步触发main处理
    threading.Thread(target=run_main_for_initial_csv, args=(save_path,)).start()
    return JSONResponse({"success": True, "msg": "初始CSV上传成功，已自动触发处理。", "path": save_path})

# 新增：补传csv接口，只允许两个子表都已完成时上传
@app.post("/upload_patch_csv")
async def upload_patch_csv(file: UploadFile = File(...)):
    allow_upload, round_id = csv_upload_state_manager.can_upload_csv()
    if not allow_upload or csv_upload_state_manager.is_handled_this_round():
        import logging
        from csv_uploader_with_monitor import WxworkSmartsheetApiClient, CORPID, CORPSECRET
        logging.warning("[系统] 存在未完成记录，用户尝试上传补传csv，禁止上传！")
        # 获取最新进度
        try:
            # 这里假设有全局docid, completeness_sheet_id, accuracy_sheet_id
            # 实际项目中建议将这些id持久化或通过配置/数据库获取
            docid = getattr(csv_upload_state_manager, 'docid', None)
            completeness_sheet_id = getattr(csv_upload_state_manager, 'completeness_sheet_id', None)
            accuracy_sheet_id = getattr(csv_upload_state_manager, 'accuracy_sheet_id', None)
            progress = {}
            if docid and completeness_sheet_id and accuracy_sheet_id:
                client = WxworkSmartsheetApiClient(CORPID)
                # 完备性
                comp_records = client.get_records(docid=docid, sheet_id=completeness_sheet_id)
                comp_total = len(comp_records)
                comp_done = sum(1 for r in comp_records if next((v.get("text", "") for v in r.get("values", {}).get("重新OCR状态", []) if v.get("text")), "") == "已完成")
                # 准确性
                acc_records = client.get_records(docid=docid, sheet_id=accuracy_sheet_id)
                acc_total = len(acc_records)
                acc_done = sum(1 for r in acc_records if next((v.get("text", "") for v in r.get("values", {}).get("重新OCR状态", []) if v.get("text")), "") == "已完成")
                progress = {
                    "completeness": {"done": comp_done, "total": comp_total, "all_done": comp_done == comp_total and comp_total > 0},
                    "accuracy": {"done": acc_done, "total": acc_total, "all_done": acc_done == acc_total and acc_total > 0},
                    "can_upload": False,
                    "msg": f"完备性已完成{comp_done}/{comp_total}，准确性已完成{acc_done}/{acc_total}，请等待全部完成后再上传。",
                    "expected_wait_seconds": None
                }
            else:
                progress = {"msg": "进度信息暂不可用", "can_upload": False}
        except Exception as e:
            progress = {"msg": f"进度信息获取失败: {e}", "can_upload": False}
        return JSONResponse({"success": False, **progress})
    save_path = "ocr_upload/patch_latest.csv"
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    with open(save_path, "wb") as buffer:
        shutil.copyfileobj(file.file, buffer)
    # 异步触发主流程
    import logging
    logging.info("[系统] 补传csv已上传并触发处理。")
    threading.Thread(target=run_main_for_initial_csv, args=(save_path,)).start()
    csv_upload_state_manager.mark_csv_handled()
    return JSONResponse({"success": True, "msg": "补传CSV上传成功，已自动触发处理。", "path": save_path})

# 启动FastAPI应用
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002, log_level="info")
